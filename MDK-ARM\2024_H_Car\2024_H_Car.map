Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C3_Init) for MX_I2C3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to scheduler.o(i.Scheduler_Init) for Scheduler_Init
    main.o(i.main) refers to scheduler.o(i.Scheduler_Run) for Scheduler_Run
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C3_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C3_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to uart_driver.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    ebtn.o(i.bit_array_cmp) refers to memcmp.o(.text) for memcmp
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_add_btn) refers to ebtn.o(i.ebtn_combo_btn_add_btn_by_idx) for ebtn_combo_btn_add_btn_by_idx
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_combo_btn_remove_btn) refers to ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx) for ebtn_combo_btn_remove_btn_by_idx
    ebtn.o(i.ebtn_combo_register) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_btn_by_key_id) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_btn_index_by_btn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_btn_dyn) refers to ebtn.o(i.ebtn_get_btn_index_by_key_id) for ebtn_get_btn_index_by_key_id
    ebtn.o(i.ebtn_get_btn_index_by_key_id) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_config) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_get_total_btn_cnt) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_init) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(i.ebtn_is_btn_in_process) for ebtn_is_btn_in_process
    ebtn.o(i.ebtn_is_in_process) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.bit_array_assign) for bit_array_assign
    ebtn.o(i.ebtn_process) refers to ebtn.o(i.ebtn_process_with_curr_state) for ebtn_process_with_curr_state
    ebtn.o(i.ebtn_process) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_btn) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_btn_combo) refers to ebtn.o(i.prv_process_btn) for prv_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_and) for bit_array_and
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_cmp) for bit_array_cmp
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_or) for bit_array_or
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.bit_array_get) for bit_array_get
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn) for ebtn_process_btn
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(i.ebtn_process_btn_combo) for ebtn_process_btn_combo
    ebtn.o(i.ebtn_process_with_curr_state) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    ebtn.o(i.ebtn_process_with_curr_state) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_register) refers to ebtn.o(i.ebtn_get_total_btn_cnt) for ebtn_get_total_btn_cnt
    ebtn.o(i.ebtn_register) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.ebtn_set_config) refers to ebtn.o(.bss) for .bss
    ebtn.o(i.prv_process_btn) refers to ebtn.o(.bss) for .bss
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_DisplayMode) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_HorizontalShift) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_Init) refers to oled.o(.data) for .data
    oled.o(i.OLED_IntensityControl) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_On) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_On) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_Set_Pos) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowCHinese) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_ShowCHinese) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WR_DATA) for OLED_WR_DATA
    oled.o(i.OLED_ShowChar) refers to oled.o(.data) for .data
    oled.o(i.OLED_ShowNum) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Showdecimal) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_Showdecimal) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Some_HorizontalShift) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_VerticalAndHorizontalShift) refers to oled.o(i.OLED_WR_CMD) for OLED_WR_CMD
    oled.o(i.OLED_WR_CMD) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_WR_CMD) refers to i2c.o(.bss) for hi2c1
    oled.o(i.OLED_WR_DATA) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_WR_DATA) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_Anolog_Normalize) refers to hardware_iic.o(i.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(i.IIC_Get_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Digtal) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Offset) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Single_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_ReadByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    hardware_iic.o(i.IIC_ReadByte) refers to i2c.o(.bss) for hi2c3
    hardware_iic.o(i.IIC_ReadBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(i.IIC_ReadBytes) refers to i2c.o(.bss) for hi2c3
    hardware_iic.o(i.IIC_WriteByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    hardware_iic.o(i.IIC_WriteByte) refers to i2c.o(.bss) for hi2c3
    hardware_iic.o(i.IIC_WriteBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(i.IIC_WriteBytes) refers to i2c.o(.bss) for hi2c3
    hardware_iic.o(i.Ping) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    iic.o(i.IIC_Ack) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Ack) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_GPIO_Init) for IIC_GPIO_Init
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_Start) for IIC_Start
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    iic.o(i.IIC_CheckDevice) refers to iic.o(i.IIC_Stop) for IIC_Stop
    iic.o(i.IIC_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    iic.o(i.IIC_GPIO_Init) refers to iic.o(i.IIC_Stop) for IIC_Stop
    iic.o(i.IIC_NAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_NAck) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Read_Byte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Read_Byte) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Read_Byte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    iic.o(i.IIC_Read_Byte) refers to iic.o(i.IIC_Ack) for IIC_Ack
    iic.o(i.IIC_Read_Byte) refers to iic.o(i.IIC_NAck) for IIC_NAck
    iic.o(i.IIC_Send_Byte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Send_Byte) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Start) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Stop) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Wait_Ack) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    iic.o(i.IIC_Wait_Ack) refers to iic.o(i.IIC_Delay) for IIC_Delay
    iic.o(i.IIC_Wait_Ack) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(i.get_accel_prod_shift) for get_accel_prod_shift
    inv_mpu.o(i.get_accel_prod_shift) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.get_accel_prod_shift) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.get_st_biases) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.get_st_biases) refers to llsdiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.gyro_self_test) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.inv_orientation_matrix_to_scalar) refers to inv_mpu.o(i.inv_row_2_scale) for inv_row_2_scale
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_dmp_get_data) refers to inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) for dmp_read_fifo
    inv_mpu.o(i.mpu_dmp_get_data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    inv_mpu.o(i.mpu_dmp_get_data) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    inv_mpu.o(i.mpu_dmp_get_data) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    inv_mpu.o(i.mpu_dmp_get_data) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    inv_mpu.o(i.mpu_dmp_get_data) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    inv_mpu.o(i.mpu_dmp_init) refers to iic.o(i.IIC_GPIO_Init) for IIC_GPIO_Init
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_init) for mpu_init
    inv_mpu.o(i.mpu_dmp_init) refers to mpu6050_driver.o(i.MPU_Get_Gyro_Offset) for MPU_Get_Gyro_Offset
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.inv_orientation_matrix_to_scalar) for inv_orientation_matrix_to_scalar
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) for dmp_set_orientation
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) for dmp_enable_feature
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) for dmp_set_fifo_rate
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.run_self_test) for run_self_test
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_dmp_init) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_accel_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_accel_reg) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_accel_sens) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_dmp_state) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_fifo_config) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_gyro_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_gyro_reg) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_gyro_reg) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_gyro_sens) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_int_status) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_int_status) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_lpf) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_power_state) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_sample_rate) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_get_temperature) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu.o(i.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(i.mpu_load_firmware) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_lp_accel_mode) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_fifo) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_fifo_stream) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_mem) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_read_reg) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_read_reg) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_reg_dump) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_reg_dump) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_reset_fifo) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_reset_fifo) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_reset_fifo) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.get_st_biases) for get_st_biases
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.accel_self_test) for accel_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.gyro_self_test) for gyro_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_accel_bias) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_accel_fsr) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_accel_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_bypass) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_set_bypass) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_set_dmp_state) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_int_latched) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_int_latched) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_int_level) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_lpf) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_lpf) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_set_sample_rate) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_set_sensors) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_set_sensors) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.mpu_write_mem) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.mpu_write_mem) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_run_self_test) for mpu_run_self_test
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_sens) for mpu_get_gyro_sens
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) for dmp_set_accel_bias
    inv_mpu.o(i.set_int_enable) refers to mpu6050.o(i.MPU_Write_Len) for MPU_Write_Len
    inv_mpu.o(i.set_int_enable) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for reg
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for hw
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for test
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to aeabi_memset.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) for dmp_enable_gyro_cal
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) for dmp_set_tap_axes
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) for dmp_set_tap_count
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) for dmp_set_tap_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) for dmp_set_tap_time_multi
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) for dmp_set_shake_reject_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) for dmp_set_shake_reject_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) for dmp_set_shake_reject_timeout
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) for dmp_enable_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) for dmp_enable_6x_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to aeabi_memset.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu.o(i.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.constdata) for .constdata
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mget_ms) for mget_ms
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    mpu6050.o(i.MPU_Get_Accelerometer) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Gyroscope) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Temperature) refers to mpu6050.o(i.MPU_Read_Len) for MPU_Read_Len
    mpu6050.o(i.MPU_Get_Temperature) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    mpu6050.o(i.MPU_Get_Temperature) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    mpu6050.o(i.MPU_Get_Temperature) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    mpu6050.o(i.MPU_Get_Temperature) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    mpu6050.o(i.MPU_Init) refers to iic.o(i.IIC_GPIO_Init) for IIC_GPIO_Init
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Gyro_Fsr) for MPU_Set_Gyro_Fsr
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Accel_Fsr) for MPU_Set_Accel_Fsr
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Set_Rate) for MPU_Set_Rate
    mpu6050.o(i.MPU_Init) refers to mpu6050.o(i.MPU_Read_Byte) for MPU_Read_Byte
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Start) for IIC_Start
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Read_Byte) for IIC_Read_Byte
    mpu6050.o(i.MPU_Read_Byte) refers to iic.o(i.IIC_Stop) for IIC_Stop
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Start) for IIC_Start
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Stop) for IIC_Stop
    mpu6050.o(i.MPU_Read_Len) refers to iic.o(i.IIC_Read_Byte) for IIC_Read_Byte
    mpu6050.o(i.MPU_Set_Accel_Fsr) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Gyro_Fsr) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_LPF) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.MPU_Write_Byte) for MPU_Write_Byte
    mpu6050.o(i.MPU_Set_Rate) refers to mpu6050.o(i.MPU_Set_LPF) for MPU_Set_LPF
    mpu6050.o(i.MPU_Write_Byte) refers to iic.o(i.IIC_Start) for IIC_Start
    mpu6050.o(i.MPU_Write_Byte) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    mpu6050.o(i.MPU_Write_Byte) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    mpu6050.o(i.MPU_Write_Byte) refers to iic.o(i.IIC_Stop) for IIC_Stop
    mpu6050.o(i.MPU_Write_Len) refers to iic.o(i.IIC_Start) for IIC_Start
    mpu6050.o(i.MPU_Write_Len) refers to iic.o(i.IIC_Send_Byte) for IIC_Send_Byte
    mpu6050.o(i.MPU_Write_Len) refers to iic.o(i.IIC_Wait_Ack) for IIC_Wait_Ack
    mpu6050.o(i.MPU_Write_Len) refers to iic.o(i.IIC_Stop) for IIC_Stop
    encoder_driver.o(i.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    key_driver.o(i.Ebtn_Init) refers to ebtn.o(i.ebtn_init) for ebtn_init
    key_driver.o(i.Ebtn_Init) refers to ebtn.o(i.ebtn_set_config) for ebtn_set_config
    key_driver.o(i.Ebtn_Init) refers to key_app.o(i.my_handle_key_event) for my_handle_key_event
    key_driver.o(i.Ebtn_Init) refers to key_driver.o(i.my_get_key_state) for my_get_key_state
    key_driver.o(i.Ebtn_Init) refers to key_driver.o(.data) for .data
    key_driver.o(i.Key_Read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_driver.o(i.my_get_key_state) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key_driver.o(.data) refers to key_driver.o(.constdata) for defaul_ebtn_param
    led_driver.o(i.Led_Display) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_driver.o(i.Led_Display) refers to led_driver.o(.data) for .data
    motor_driver.o(i.Motor_Brake) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_Config_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_Config_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_driver.o(i.Motor_Set_Speed) refers to motor_driver.o(i.Motor_Limit_Speed) for Motor_Limit_Speed
    motor_driver.o(i.Motor_Set_Speed) refers to motor_driver.o(i.Motor_Dead_Compensation) for Motor_Dead_Compensation
    motor_driver.o(i.Motor_Set_Speed) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver.o(i.Motor_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled_driver.o(i.OLED_SendBuff) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled_driver.o(i.OLED_SendBuff) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled_driver.o(i.OLED_SendBuff) refers to i2c.o(.bss) for hi2c1
    oled_driver.o(i.Oled_Printf) refers to vsnprintf.o(.text) for vsnprintf
    oled_driver.o(i.Oled_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to uart_driver.o(.bss) for .bss
    uart_driver.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    uart_driver.o(i.Uart_Printf) refers to vsnprintf.o(.text) for vsnprintf
    uart_driver.o(i.Uart_Printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    mpu6050_driver.o(i.MPU_Get_Gyro_Offset) refers to mpu6050.o(i.MPU_Get_Gyroscope) for MPU_Get_Gyroscope
    mpu6050_driver.o(i.MPU_Get_Gyro_Offset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    mpu6050_driver.o(i.convert_to_continuous_yaw) refers to mpu6050_driver.o(.data) for .data
    encoder_app.o(i.Encoder_Init) refers to encoder_driver.o(i.Encoder_Driver_Init) for Encoder_Driver_Init
    encoder_app.o(i.Encoder_Init) refers to tim.o(.bss) for htim3
    encoder_app.o(i.Encoder_Init) refers to encoder_app.o(.bss) for .bss
    encoder_app.o(i.Encoder_Task) refers to encoder_driver.o(i.Encoder_Driver_Update) for Encoder_Driver_Update
    encoder_app.o(i.Encoder_Task) refers to encoder_app.o(.bss) for .bss
    gray_app.o(i.Gray_Task) refers to hardware_iic.o(i.IIC_Get_Digtal) for IIC_Get_Digtal
    gray_app.o(i.Gray_Task) refers to gray_app.o(.data) for .data
    key_app.o(i.Key_Init) refers to key_driver.o(i.Ebtn_Init) for Ebtn_Init
    key_app.o(i.Key_Task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    key_app.o(i.Key_Task) refers to ebtn.o(i.ebtn_process) for ebtn_process
    key_app.o(i.my_handle_key_event) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    key_app.o(i.my_handle_key_event) refers to scheduler_task.o(.data) for system_mode
    key_app.o(i.my_handle_key_event) refers to pid_app.o(.data) for pid_running
    key_app.o(i.my_handle_key_event) refers to usart.o(.bss) for huart1
    led_app.o(i.Led_Init) refers to led_driver.o(i.Led_Display) for Led_Display
    led_app.o(i.Led_Task) refers to led_driver.o(i.Led_Display) for Led_Display
    led_app.o(i.Led_Task) refers to led_app.o(.data) for .data
    motor_app.o(i.Motor_Init) refers to motor_driver.o(i.Motor_Config_Init) for Motor_Config_Init
    motor_app.o(i.Motor_Init) refers to tim.o(.bss) for htim1
    motor_app.o(i.Motor_Init) refers to motor_app.o(.bss) for .bss
    oled_app.o(i.Oled_Init) refers to oled.o(i.OLED_Init) for OLED_Init
    oled_app.o(i.Oled_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled_app.o(i.Oled_Task) refers to oled_driver.o(i.Oled_Printf) for Oled_Printf
    oled_app.o(i.Oled_Task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled_app.o(i.Oled_Task) refers to scheduler_task.o(.data) for system_mode
    oled_app.o(i.Oled_Task) refers to gray_app.o(.data) for Digtal
    oled_app.o(i.Oled_Task) refers to encoder_app.o(.bss) for left_encoder
    oled_app.o(i.Oled_Task) refers to mpu6050_app.o(.data) for Yaw
    pid_app.o(i.Angle_PID_control) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.Angle_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.Angle_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.Angle_PID_control) refers to mpu6050_app.o(.data) for Yaw
    pid_app.o(i.Angle_PID_control) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.Angle_PID_control) refers to pid_app.o(.data) for .data
    pid_app.o(i.Line_PID_control) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.Line_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.Line_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.Line_PID_control) refers to gray_app.o(.data) for g_line_position_error
    pid_app.o(i.Line_PID_control) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.Line_PID_control) refers to pid_app.o(.data) for .data
    pid_app.o(i.PID_Init) refers to pid.o(i.pid_init) for pid_init
    pid_app.o(i.PID_Init) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.PID_Init) refers to pid_app.o(.data) for .data
    pid_app.o(i.PID_Init) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.PID_Task) refers to pid_app.o(i.Line_PID_control) for Line_PID_control
    pid_app.o(i.PID_Task) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.PID_Task) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.PID_Task) refers to motor_driver.o(i.Motor_Set_Speed) for Motor_Set_Speed
    pid_app.o(i.PID_Task) refers to pid_app.o(i.Angle_PID_control) for Angle_PID_control
    pid_app.o(i.PID_Task) refers to pid_app.o(.data) for .data
    pid_app.o(i.PID_Task) refers to encoder_app.o(.bss) for left_encoder
    pid_app.o(i.PID_Task) refers to pid_app.o(.bss) for .bss
    pid_app.o(i.PID_Task) refers to motor_app.o(.bss) for left_motor
    pid_app.o(i.PID_Task) refers to scheduler_task.o(.data) for distance
    uart_app.o(i.Uart_Init) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    uart_app.o(i.Uart_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer_input
    uart_app.o(i.Uart_Init) refers to uart_driver.o(.bss) for ring_buffer
    uart_app.o(i.Uart_Init) refers to usart.o(.bss) for huart1
    uart_app.o(i.Uart_Task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_app.o(i.Uart_Task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_app.o(i.Uart_Task) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    uart_app.o(i.Uart_Task) refers to rt_memclr.o(.text) for __aeabi_memclr
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for ring_buffer
    uart_app.o(i.Uart_Task) refers to uart_driver.o(.bss) for uart_data_buffer
    uart_app.o(i.Uart_Task) refers to usart.o(.bss) for huart1
    mpu6050_app.o(i.Mpu6050_Init) refers to mpu6050.o(i.MPU_Init) for MPU_Init
    mpu6050_app.o(i.Mpu6050_Init) refers to inv_mpu.o(i.mpu_dmp_init) for mpu_dmp_init
    mpu6050_app.o(i.Mpu6050_Task) refers to inv_mpu.o(i.mpu_dmp_get_data) for mpu_dmp_get_data
    mpu6050_app.o(i.Mpu6050_Task) refers to mpu6050_driver.o(i.convert_to_continuous_yaw) for convert_to_continuous_yaw
    mpu6050_app.o(i.Mpu6050_Task) refers to mpu6050_app.o(.data) for .data
    scheduler.o(i.Scheduler_Init) refers to scheduler_task.o(i.System_Init) for System_Init
    scheduler.o(i.Scheduler_Init) refers to scheduler.o(.data) for .data
    scheduler.o(i.Scheduler_Run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    scheduler.o(i.Scheduler_Run) refers to scheduler.o(.data) for .data
    scheduler.o(.data) refers to led_app.o(i.Led_Task) for Led_Task
    scheduler.o(.data) refers to oled_app.o(i.Oled_Task) for Oled_Task
    scheduler_task.o(i.Car_State_Update) refers to motor_driver.o(i.Motor_Brake) for Motor_Brake
    scheduler_task.o(i.Car_State_Update) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    scheduler_task.o(i.Car_State_Update) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    scheduler_task.o(i.Car_State_Update) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    scheduler_task.o(i.Car_State_Update) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    scheduler_task.o(i.Car_State_Update) refers to pid.o(i.pid_set_target) for pid_set_target
    scheduler_task.o(i.Car_State_Update) refers to pid.o(i.pid_reset) for pid_reset
    scheduler_task.o(i.Car_State_Update) refers to led_app.o(.data) for led_state
    scheduler_task.o(i.Car_State_Update) refers to scheduler_task.o(.data) for .data
    scheduler_task.o(i.Car_State_Update) refers to pid_app.o(.data) for pid_running
    scheduler_task.o(i.Car_State_Update) refers to motor_app.o(.bss) for left_motor
    scheduler_task.o(i.Car_State_Update) refers to pid_app.o(.bss) for pid_angle
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to key_app.o(i.Key_Task) for Key_Task
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to encoder_app.o(i.Encoder_Task) for Encoder_Task
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to mpu6050_app.o(i.Mpu6050_Task) for Mpu6050_Task
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to gray_app.o(i.Gray_Task) for Gray_Task
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to pid_app.o(i.PID_Task) for PID_Task
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to scheduler_task.o(i.Car_State_Update) for Car_State_Update
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to tim.o(.bss) for htim2
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to scheduler_task.o(.data) for .data
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to encoder_app.o(.bss) for left_encoder
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to gray_app.o(.data) for Digtal
    scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback) refers to led_app.o(.data) for led_state
    scheduler_task.o(i.System_Init) refers to led_app.o(i.Led_Init) for Led_Init
    scheduler_task.o(i.System_Init) refers to key_app.o(i.Key_Init) for Key_Init
    scheduler_task.o(i.System_Init) refers to oled_app.o(i.Oled_Init) for Oled_Init
    scheduler_task.o(i.System_Init) refers to uart_app.o(i.Uart_Init) for Uart_Init
    scheduler_task.o(i.System_Init) refers to gray_app.o(i.Gray_Init) for Gray_Init
    scheduler_task.o(i.System_Init) refers to motor_app.o(i.Motor_Init) for Motor_Init
    scheduler_task.o(i.System_Init) refers to encoder_app.o(i.Encoder_Init) for Encoder_Init
    scheduler_task.o(i.System_Init) refers to mpu6050_app.o(i.Mpu6050_Init) for Mpu6050_Init
    scheduler_task.o(i.System_Init) refers to pid_app.o(i.PID_Init) for PID_Init
    scheduler_task.o(i.System_Init) refers to uart_driver.o(i.Uart_Printf) for Uart_Printf
    scheduler_task.o(i.System_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    scheduler_task.o(i.System_Init) refers to usart.o(.bss) for huart1
    scheduler_task.o(i.System_Init) refers to tim.o(.bss) for htim2
    llsdiv.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    asin.o(i.__hardfp_asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.__hardfp_asin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    asin.o(i.__hardfp_asin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    asin.o(i.__hardfp_asin) refers to _rserrno.o(.text) for __set_errno
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.__hardfp_asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.__hardfp_asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.__hardfp_asin) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    asin.o(i.__hardfp_asin) refers to fabs.o(i.fabs) for fabs
    asin.o(i.__hardfp_asin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    asin.o(i.__hardfp_asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.__hardfp_asin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    asin.o(i.__hardfp_asin) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    asin.o(i.__hardfp_asin) refers to asin.o(.constdata) for .constdata
    asin.o(i.__softfp_asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(i.asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.____hardfp_asin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    asin_x.o(i.____hardfp_asin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    asin_x.o(i.____hardfp_asin$lsc) refers to _rserrno.o(.text) for __set_errno
    asin_x.o(i.____hardfp_asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.____hardfp_asin$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    asin_x.o(i.____hardfp_asin$lsc) refers to fabs.o(i.fabs) for fabs
    asin_x.o(i.____hardfp_asin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    asin_x.o(i.____hardfp_asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.____hardfp_asin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    asin_x.o(i.____hardfp_asin$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    asin_x.o(i.____hardfp_asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.__hardfp_atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.__hardfp_atan) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.____hardfp_atan$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (100 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (52 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (72 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (100 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn), (26 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_add_btn_by_idx), (22 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn), (26 bytes).
    Removing ebtn.o(i.ebtn_combo_btn_remove_btn_by_idx), (22 bytes).
    Removing ebtn.o(i.ebtn_combo_register), (44 bytes).
    Removing ebtn.o(i.ebtn_get_btn_by_key_id), (72 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn), (6 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_btn_dyn), (6 bytes).
    Removing ebtn.o(i.ebtn_get_btn_index_by_key_id), (64 bytes).
    Removing ebtn.o(i.ebtn_get_config), (12 bytes).
    Removing ebtn.o(i.ebtn_get_total_btn_cnt), (24 bytes).
    Removing ebtn.o(i.ebtn_is_btn_active), (16 bytes).
    Removing ebtn.o(i.ebtn_is_btn_in_process), (16 bytes).
    Removing ebtn.o(i.ebtn_is_in_process), (116 bytes).
    Removing ebtn.o(i.ebtn_register), (56 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_get), (110 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (70 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (70 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (164 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (92 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (8 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_DisplayMode), (4 bytes).
    Removing oled.o(i.OLED_Display_Off), (24 bytes).
    Removing oled.o(i.OLED_Display_On), (24 bytes).
    Removing oled.o(i.OLED_DrawBMP), (72 bytes).
    Removing oled.o(i.OLED_HorizontalShift), (62 bytes).
    Removing oled.o(i.OLED_IntensityControl), (20 bytes).
    Removing oled.o(i.OLED_On), (52 bytes).
    Removing oled.o(i.OLED_ShowCHinese), (92 bytes).
    Removing oled.o(i.OLED_ShowNum), (116 bytes).
    Removing oled.o(i.OLED_Showdecimal), (310 bytes).
    Removing oled.o(i.OLED_Some_HorizontalShift), (66 bytes).
    Removing oled.o(i.OLED_VerticalAndHorizontalShift), (68 bytes).
    Removing oled.o(i.oled_pow), (16 bytes).
    Removing oled.o(.constdata), (192 bytes).
    Removing hardware_iic.o(.rev16_text), (4 bytes).
    Removing hardware_iic.o(.revsh_text), (4 bytes).
    Removing hardware_iic.o(.rrx_text), (6 bytes).
    Removing hardware_iic.o(i.IIC_Anolog_Normalize), (16 bytes).
    Removing hardware_iic.o(i.IIC_Get_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_Get_Offset), (24 bytes).
    Removing hardware_iic.o(i.IIC_Get_Single_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_ReadByte), (32 bytes).
    Removing hardware_iic.o(i.IIC_WriteByte), (44 bytes).
    Removing hardware_iic.o(i.IIC_WriteBytes), (36 bytes).
    Removing hardware_iic.o(i.Ping), (30 bytes).
    Removing pid.o(i.pid_app_limit_integral), (36 bytes).
    Removing pid.o(i.pid_calculate_incremental), (122 bytes).
    Removing pid.o(i.pid_set_limit), (6 bytes).
    Removing pid.o(i.pid_set_params), (6 bytes).
    Removing iic.o(.rev16_text), (4 bytes).
    Removing iic.o(.revsh_text), (4 bytes).
    Removing iic.o(.rrx_text), (6 bytes).
    Removing iic.o(i.IIC_CheckDevice), (32 bytes).
    Removing inv_mpu.o(.rev16_text), (4 bytes).
    Removing inv_mpu.o(.revsh_text), (4 bytes).
    Removing inv_mpu.o(.rrx_text), (6 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_reg), (72 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_fsr), (6 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_reg), (6 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_dmp_state), (16 bytes).
    Removing inv_mpu.o(i.mpu_get_fifo_config), (16 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_reg), (72 bytes).
    Removing inv_mpu.o(i.mpu_get_int_status), (52 bytes).
    Removing inv_mpu.o(i.mpu_get_power_state), (20 bytes).
    Removing inv_mpu.o(i.mpu_get_temperature), (124 bytes).
    Removing inv_mpu.o(i.mpu_lp_motion_interrupt), (432 bytes).
    Removing inv_mpu.o(i.mpu_read_fifo), (340 bytes).
    Removing inv_mpu.o(i.mpu_read_reg), (56 bytes).
    Removing inv_mpu.o(i.mpu_reg_dump), (64 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_bias), (220 bytes).
    Removing inv_mpu.o(i.mpu_set_compass_sample_rate), (6 bytes).
    Removing inv_mpu.o(i.mpu_set_int_level), (16 bytes).
    Removing inv_mpu.o(i.setup_compass), (6 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rev16_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.revsh_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rrx_text), (6 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count), (56 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time), (62 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb), (12 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode), (84 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count), (38 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time), (44 bytes).
    Removing mpu6050.o(.rev16_text), (4 bytes).
    Removing mpu6050.o(.revsh_text), (4 bytes).
    Removing mpu6050.o(.rrx_text), (6 bytes).
    Removing mpu6050.o(i.MPU_Get_Accelerometer), (50 bytes).
    Removing mpu6050.o(i.MPU_Get_Temperature), (100 bytes).
    Removing encoder_driver.o(.rev16_text), (4 bytes).
    Removing encoder_driver.o(.revsh_text), (4 bytes).
    Removing encoder_driver.o(.rrx_text), (6 bytes).
    Removing key_driver.o(.rev16_text), (4 bytes).
    Removing key_driver.o(.revsh_text), (4 bytes).
    Removing key_driver.o(.rrx_text), (6 bytes).
    Removing key_driver.o(i.Key_Read), (28 bytes).
    Removing led_driver.o(.rev16_text), (4 bytes).
    Removing led_driver.o(.revsh_text), (4 bytes).
    Removing led_driver.o(.rrx_text), (6 bytes).
    Removing motor_driver.o(.rev16_text), (4 bytes).
    Removing motor_driver.o(.revsh_text), (4 bytes).
    Removing motor_driver.o(.rrx_text), (6 bytes).
    Removing motor_driver.o(i.Motor_Stop), (68 bytes).
    Removing oled_driver.o(.rev16_text), (4 bytes).
    Removing oled_driver.o(.revsh_text), (4 bytes).
    Removing oled_driver.o(.rrx_text), (6 bytes).
    Removing oled_driver.o(i.OLED_SendBuff), (56 bytes).
    Removing uart_driver.o(.rev16_text), (4 bytes).
    Removing uart_driver.o(.revsh_text), (4 bytes).
    Removing uart_driver.o(.rrx_text), (6 bytes).
    Removing uart_driver.o(.bss), (128 bytes).
    Removing mpu6050_driver.o(.rev16_text), (4 bytes).
    Removing mpu6050_driver.o(.revsh_text), (4 bytes).
    Removing mpu6050_driver.o(.rrx_text), (6 bytes).
    Removing encoder_app.o(.rev16_text), (4 bytes).
    Removing encoder_app.o(.revsh_text), (4 bytes).
    Removing encoder_app.o(.rrx_text), (6 bytes).
    Removing gray_app.o(.rev16_text), (4 bytes).
    Removing gray_app.o(.revsh_text), (4 bytes).
    Removing gray_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing motor_app.o(.rev16_text), (4 bytes).
    Removing motor_app.o(.revsh_text), (4 bytes).
    Removing motor_app.o(.rrx_text), (6 bytes).
    Removing motor_app.o(i.Motor_Task), (2 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing pid_app.o(.rev16_text), (4 bytes).
    Removing pid_app.o(.revsh_text), (4 bytes).
    Removing pid_app.o(.rrx_text), (6 bytes).
    Removing uart_app.o(.rev16_text), (4 bytes).
    Removing uart_app.o(.revsh_text), (4 bytes).
    Removing uart_app.o(.rrx_text), (6 bytes).
    Removing uart_app.o(i.Uart_Task), (84 bytes).
    Removing mpu6050_app.o(.rev16_text), (4 bytes).
    Removing mpu6050_app.o(.revsh_text), (4 bytes).
    Removing mpu6050_app.o(.rrx_text), (6 bytes).
    Removing scheduler.o(.rev16_text), (4 bytes).
    Removing scheduler.o(.revsh_text), (4 bytes).
    Removing scheduler.o(.rrx_text), (6 bytes).
    Removing scheduler_task.o(.rev16_text), (4 bytes).
    Removing scheduler_task.o(.revsh_text), (4 bytes).
    Removing scheduler_task.o(.rrx_text), (6 bytes).

604 unused section(s) (total 43956 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\User\App\encoder_app.c                0x00000000   Number         0  encoder_app.o ABSOLUTE
    ..\User\App\gray_app.c                   0x00000000   Number         0  gray_app.o ABSOLUTE
    ..\User\App\key_app.c                    0x00000000   Number         0  key_app.o ABSOLUTE
    ..\User\App\led_app.c                    0x00000000   Number         0  led_app.o ABSOLUTE
    ..\User\App\motor_app.c                  0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\User\App\mpu6050_app.c                0x00000000   Number         0  mpu6050_app.o ABSOLUTE
    ..\User\App\oled_app.c                   0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\User\App\pid_app.c                    0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\User\App\uart_app.c                   0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\User\Driver\encoder_driver.c          0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\User\Driver\key_driver.c              0x00000000   Number         0  key_driver.o ABSOLUTE
    ..\User\Driver\led_driver.c              0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\User\Driver\motor_driver.c            0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\User\Driver\mpu6050_driver.c          0x00000000   Number         0  mpu6050_driver.o ABSOLUTE
    ..\User\Driver\oled_driver.c             0x00000000   Number         0  oled_driver.o ABSOLUTE
    ..\User\Driver\uart_driver.c             0x00000000   Number         0  uart_driver.o ABSOLUTE
    ..\User\Module\0.96 Oled\oled.c          0x00000000   Number         0  oled.o ABSOLUTE
    ..\User\Module\0.96 Oled\oled_font.c     0x00000000   Number         0  oled_font.o ABSOLUTE
    ..\User\Module\Ebtn\ebtn.c               0x00000000   Number         0  ebtn.o ABSOLUTE
    ..\User\Module\Grayscale\hardware_iic.c  0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\User\Module\MPU6050\IIC.c             0x00000000   Number         0  iic.o ABSOLUTE
    ..\User\Module\MPU6050\inv_mpu.c         0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\User\Module\MPU6050\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\User\Module\MPU6050\mpu6050.c         0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\User\Module\PID\pid.c                 0x00000000   Number         0  pid.o ABSOLUTE
    ..\User\Module\Ringbuffer\ringbuffer.c   0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\User\Scheduler.c                      0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\User\Scheduler_Task.c                 0x00000000   Number         0  scheduler_task.o ABSOLUTE
    ..\\User\\App\\encoder_app.c             0x00000000   Number         0  encoder_app.o ABSOLUTE
    ..\\User\\App\\gray_app.c                0x00000000   Number         0  gray_app.o ABSOLUTE
    ..\\User\\App\\key_app.c                 0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\User\\App\\led_app.c                 0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\User\\App\\motor_app.c               0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\\User\\App\\mpu6050_app.c             0x00000000   Number         0  mpu6050_app.o ABSOLUTE
    ..\\User\\App\\oled_app.c                0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\User\\App\\pid_app.c                 0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\\User\\App\\uart_app.c                0x00000000   Number         0  uart_app.o ABSOLUTE
    ..\\User\\Driver\\encoder_driver.c       0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\\User\\Driver\\key_driver.c           0x00000000   Number         0  key_driver.o ABSOLUTE
    ..\\User\\Driver\\led_driver.c           0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\\User\\Driver\\motor_driver.c         0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\\User\\Driver\\mpu6050_driver.c       0x00000000   Number         0  mpu6050_driver.o ABSOLUTE
    ..\\User\\Driver\\oled_driver.c          0x00000000   Number         0  oled_driver.o ABSOLUTE
    ..\\User\\Driver\\uart_driver.c          0x00000000   Number         0  uart_driver.o ABSOLUTE
    ..\\User\\Module\\0.96 Oled\\oled.c      0x00000000   Number         0  oled.o ABSOLUTE
    ..\\User\\Module\\Grayscale\\hardware_iic.c 0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\\User\\Module\\MPU6050\\IIC.c         0x00000000   Number         0  iic.o ABSOLUTE
    ..\\User\\Module\\MPU6050\\inv_mpu.c     0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\\User\\Module\\MPU6050\\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\\User\\Module\\MPU6050\\mpu6050.c     0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\\User\\Scheduler.c                    0x00000000   Number         0  scheduler.o ABSOLUTE
    ..\\User\\Scheduler_Task.c               0x00000000   Number         0  scheduler_task.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x0800023c   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000242   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000248   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800024e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000254   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800025a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000260   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800026a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000270   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000276   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800027c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000282   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000288   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800028e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000294   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800029a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080002a0   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080002a6   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080002b0   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002b6   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002bc   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002c2   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002c8   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002cc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002ce   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080002d2   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002d8   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080002d8   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002e4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002e4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ee   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ee   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002f0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002f2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002f2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002f4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002f4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002f4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002fa   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002fa   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002fe   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002fe   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000306   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000308   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000308   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800030c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000314   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x08000314   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000354   Section      238  lludivv7m.o(.text)
    .text                                    0x08000442   Section       72  llsdiv.o(.text)
    .text                                    0x0800048c   Section        0  vsnprintf.o(.text)
    .text                                    0x080004c0   Section        0  memcmp.o(.text)
    .text                                    0x08000518   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080005a2   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000606   Section       16  aeabi_memset.o(.text)
    .text                                    0x08000616   Section       68  rt_memclr.o(.text)
    .text                                    0x0800065a   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080006a8   Section        0  heapauxi.o(.text)
    .text                                    0x080006ae   Section        0  _rserrno.o(.text)
    .text                                    0x080006c4   Section        0  _printf_pad.o(.text)
    .text                                    0x08000712   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000736   Section        0  _printf_str.o(.text)
    .text                                    0x08000788   Section        0  _printf_dec.o(.text)
    .text                                    0x08000800   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000828   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000829   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000858   Section        0  _sputc.o(.text)
    .text                                    0x08000862   Section        0  _snputc.o(.text)
    .text                                    0x08000874   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000930   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x080009ac   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x080009ad   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000a1c   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000a1d   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000ab0   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000c38   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000c40   Section      138  lludiv10.o(.text)
    .text                                    0x08000cca   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000d7c   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000d7f   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x0800119c   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001498   Section        0  _printf_char.o(.text)
    .text                                    0x080014c4   Section        0  _printf_wchar.o(.text)
    .text                                    0x080014f0   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001530   Section        8  libspace.o(.text)
    .text                                    0x08001538   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001584   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001594   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x0800159c   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x0800161c   Section        0  bigflt0.o(.text)
    .text                                    0x08001700   Section        0  exit.o(.text)
    .text                                    0x08001714   Section      128  strcmpv7m.o(.text)
    .text                                    0x08001794   Section        0  sys_exit.o(.text)
    .text                                    0x080017a0   Section        2  use_no_semi.o(.text)
    .text                                    0x080017a2   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x080017a2   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080017e0   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001826   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001886   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001bbe   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001c9a   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001cc4   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001cee   Section      580  btod.o(CL$$btod_mult_common)
    i.Angle_PID_control                      0x08001f34   Section        0  pid_app.o(i.Angle_PID_control)
    i.BusFault_Handler                       0x08001f9c   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.Car_State_Update                       0x08001fa0   Section        0  scheduler_task.o(i.Car_State_Update)
    i.DMA2_Stream2_IRQHandler                0x080020d0   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x080020dc   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x080020dd   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08002104   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08002105   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08002158   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08002159   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08002180   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Ebtn_Init                              0x08002184   Section        0  key_driver.o(i.Ebtn_Init)
    i.Encoder_Driver_Init                    0x080021b8   Section        0  encoder_driver.o(i.Encoder_Driver_Init)
    i.Encoder_Driver_Update                  0x080021e4   Section        0  encoder_driver.o(i.Encoder_Driver_Update)
    i.Encoder_Init                           0x08002238   Section        0  encoder_app.o(i.Encoder_Init)
    i.Encoder_Task                           0x08002260   Section        0  encoder_app.o(i.Encoder_Task)
    i.Error_Handler                          0x08002278   Section        0  main.o(i.Error_Handler)
    i.Gray_Init                              0x0800227c   Section        0  gray_app.o(i.Gray_Init)
    i.Gray_Task                              0x08002280   Section        0  gray_app.o(i.Gray_Task)
    i.HAL_DMA_Abort                          0x080022dc   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x0800236e   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002394   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002534   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002608   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08002678   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x0800269c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x0800288c   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x08002896   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080028a0   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x080028ac   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Mem_Read                       0x08002a34   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    i.HAL_I2C_Mem_Write                      0x08002c38   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x08002d68   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08002e50   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002e60   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002e94   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002ed4   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002f04   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002f20   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002f60   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08002f84   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x080030b8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080030d8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080030f8   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08003158   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x080034c4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x080034ec   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x080034ee   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x080034f0   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003544   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080035d4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003630   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x08003684   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08003704   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x080037e0   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08003884   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08003928   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x080039b6   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x080039b8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08003ae8   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08003b3c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08003b3e   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08003c0a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08003c64   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x08003c66   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x08003c68   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08003d30   Section        0  scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x08003e28   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08003e2a   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08003e74   Section        0  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08003ec0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x08003f30   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08003f34   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080041b4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004218   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08004318   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x0800431a   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x0800431c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x080043bc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x080043be   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x080043c0   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x080043c1   Thumb Code    46  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_RequestMemoryRead                  0x080043f0   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    I2C_RequestMemoryRead                    0x080043f1   Thumb Code   246  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead)
    i.I2C_RequestMemoryWrite                 0x080044ec   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x080044ed   Thumb Code   162  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08004594   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08004595   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x080045ec   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x080045ed   Thumb Code   144  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x0800467c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x0800467d   Thumb Code   188  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08004738   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08004739   Thumb Code   112  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x080047a8   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x080047a9   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.IIC_Ack                                0x08004800   Section        0  iic.o(i.IIC_Ack)
    i.IIC_Delay                              0x08004840   Section        0  iic.o(i.IIC_Delay)
    IIC_Delay                                0x08004841   Thumb Code    12  iic.o(i.IIC_Delay)
    i.IIC_GPIO_Init                          0x0800484c   Section        0  iic.o(i.IIC_GPIO_Init)
    i.IIC_Get_Digtal                         0x08004888   Section        0  hardware_iic.o(i.IIC_Get_Digtal)
    i.IIC_NAck                               0x0800489c   Section        0  iic.o(i.IIC_NAck)
    i.IIC_ReadBytes                          0x080048d4   Section        0  hardware_iic.o(i.IIC_ReadBytes)
    i.IIC_Read_Byte                          0x080048f8   Section        0  iic.o(i.IIC_Read_Byte)
    i.IIC_Send_Byte                          0x08004950   Section        0  iic.o(i.IIC_Send_Byte)
    i.IIC_Start                              0x080049ac   Section        0  iic.o(i.IIC_Start)
    i.IIC_Stop                               0x080049ec   Section        0  iic.o(i.IIC_Stop)
    i.IIC_Wait_Ack                           0x08004a1c   Section        0  iic.o(i.IIC_Wait_Ack)
    i.Key_Init                               0x08004a64   Section        0  key_app.o(i.Key_Init)
    i.Key_Task                               0x08004a68   Section        0  key_app.o(i.Key_Task)
    i.Led_Display                            0x08004a78   Section        0  led_driver.o(i.Led_Display)
    i.Led_Init                               0x08004aa0   Section        0  led_app.o(i.Led_Init)
    i.Led_Task                               0x08004aa8   Section        0  led_app.o(i.Led_Task)
    i.Line_PID_control                       0x08004ab4   Section        0  pid_app.o(i.Line_PID_control)
    i.MPU_Get_Gyro_Offset                    0x08004b1c   Section        0  mpu6050_driver.o(i.MPU_Get_Gyro_Offset)
    i.MPU_Get_Gyroscope                      0x08004b78   Section        0  mpu6050.o(i.MPU_Get_Gyroscope)
    i.MPU_Init                               0x08004baa   Section        0  mpu6050.o(i.MPU_Init)
    i.MPU_Read_Byte                          0x08004c20   Section        0  mpu6050.o(i.MPU_Read_Byte)
    i.MPU_Read_Len                           0x08004c5c   Section        0  mpu6050.o(i.MPU_Read_Len)
    i.MPU_Set_Accel_Fsr                      0x08004cc8   Section        0  mpu6050.o(i.MPU_Set_Accel_Fsr)
    i.MPU_Set_Gyro_Fsr                       0x08004cd2   Section        0  mpu6050.o(i.MPU_Set_Gyro_Fsr)
    i.MPU_Set_LPF                            0x08004cdc   Section        0  mpu6050.o(i.MPU_Set_LPF)
    i.MPU_Set_Rate                           0x08004d0c   Section        0  mpu6050.o(i.MPU_Set_Rate)
    i.MPU_Write_Byte                         0x08004d3a   Section        0  mpu6050.o(i.MPU_Write_Byte)
    i.MPU_Write_Len                          0x08004d76   Section        0  mpu6050.o(i.MPU_Write_Len)
    i.MX_DMA_Init                            0x08004dc8   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08004df4   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08004ef0   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C3_Init                           0x08004f30   Section        0  i2c.o(i.MX_I2C3_Init)
    i.MX_TIM1_Init                           0x08004f70   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x08005048   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x080050ac   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08005118   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_USART1_UART_Init                    0x08005184   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x080051bc   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MemManage_Handler                      0x080051f4   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Motor_Brake                            0x080051f6   Section        0  motor_driver.o(i.Motor_Brake)
    i.Motor_Config_Init                      0x0800523a   Section        0  motor_driver.o(i.Motor_Config_Init)
    i.Motor_Dead_Compensation                0x080052a8   Section        0  motor_driver.o(i.Motor_Dead_Compensation)
    i.Motor_Init                             0x080052c8   Section        0  motor_app.o(i.Motor_Init)
    i.Motor_Limit_Speed                      0x0800531c   Section        0  motor_driver.o(i.Motor_Limit_Speed)
    i.Motor_Set_Speed                        0x0800532e   Section        0  motor_driver.o(i.Motor_Set_Speed)
    i.Mpu6050_Init                           0x080053d2   Section        0  mpu6050_app.o(i.Mpu6050_Init)
    i.Mpu6050_Task                           0x080053e0   Section        0  mpu6050_app.o(i.Mpu6050_Task)
    i.NMI_Handler                            0x08005404   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08005406   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x0800543c   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Pos                           0x0800545c   Section        0  oled.o(i.OLED_Set_Pos)
    i.OLED_ShowChar                          0x08005480   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowString                        0x0800551c   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_WR_CMD                            0x08005574   Section        0  oled.o(i.OLED_WR_CMD)
    i.OLED_WR_DATA                           0x08005598   Section        0  oled.o(i.OLED_WR_DATA)
    i.Oled_Init                              0x080055bc   Section        0  oled_app.o(i.Oled_Init)
    i.Oled_Printf                            0x080055ca   Section        0  oled_driver.o(i.Oled_Printf)
    i.Oled_Task                              0x080055fc   Section        0  oled_app.o(i.Oled_Task)
    i.PID_Init                               0x08005734   Section        0  pid_app.o(i.PID_Init)
    i.PID_Task                               0x080057e8   Section        0  pid_app.o(i.PID_Task)
    i.PendSV_Handler                         0x080058b0   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x080058b2   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Scheduler_Init                         0x080058b4   Section        0  scheduler.o(i.Scheduler_Init)
    i.Scheduler_Run                          0x080058c8   Section        0  scheduler.o(i.Scheduler_Run)
    i.SysTick_Handler                        0x08005904   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08005908   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x0800599c   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.System_Init                            0x080059ac   Section        0  scheduler_task.o(i.System_Init)
    i.TIM2_IRQHandler                        0x08005a04   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM_Base_SetConfig                     0x08005a10   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08005ae0   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x08005afa   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08005b0e   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08005b0f   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x08005b20   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x08005b21   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08005b80   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08005bec   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08005bed   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08005c54   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08005c55   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08005ca4   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08005ca5   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08005cc6   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08005cc7   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x08005cea   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005ceb   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08005cf8   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08005cf9   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08005d42   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08005d43   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08005dc8   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08005dc9   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08005de6   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005de7   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x08005e34   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08005e35   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08005e50   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005e51   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08005f14   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08005f15   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08006020   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_WaitOnFlagUntilTimeout            0x080060c0   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080060c1   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08006134   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08006140   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.Uart_Init                              0x0800614c   Section        0  uart_app.o(i.Uart_Init)
    i.Uart_Printf                            0x08006184   Section        0  uart_driver.o(i.Uart_Printf)
    i.UsageFault_Handler                     0x080061b6   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x080061b8   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x080061e8   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080061e9   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_asin                          0x08006208   Section        0  asin.o(i.__hardfp_asin)
    i.__hardfp_atan                          0x08006568   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_atan2                         0x08006840   Section        0  atan2.o(i.__hardfp_atan2)
    i.__kernel_poly                          0x08006a40   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x08006b38   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x08006b4c   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08006b60   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x08006b80   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x08006ba0   Section        0  __printf_wp.o(i._is_digit)
    i.accel_self_test                        0x08006bb0   Section        0  inv_mpu.o(i.accel_self_test)
    accel_self_test                          0x08006bb1   Thumb Code   126  inv_mpu.o(i.accel_self_test)
    i.atan                                   0x08006c40   Section        0  atan.o(i.atan)
    i.bit_array_and                          0x08006c50   Section        0  ebtn.o(i.bit_array_and)
    bit_array_and                            0x08006c51   Thumb Code    38  ebtn.o(i.bit_array_and)
    i.bit_array_assign                       0x08006c76   Section        0  ebtn.o(i.bit_array_assign)
    bit_array_assign                         0x08006c77   Thumb Code    30  ebtn.o(i.bit_array_assign)
    i.bit_array_cmp                          0x08006c94   Section        0  ebtn.o(i.bit_array_cmp)
    bit_array_cmp                            0x08006c95   Thumb Code    12  ebtn.o(i.bit_array_cmp)
    i.bit_array_get                          0x08006ca0   Section        0  ebtn.o(i.bit_array_get)
    bit_array_get                            0x08006ca1   Thumb Code    18  ebtn.o(i.bit_array_get)
    i.bit_array_or                           0x08006cb4   Section        0  ebtn.o(i.bit_array_or)
    bit_array_or                             0x08006cb5   Thumb Code    38  ebtn.o(i.bit_array_or)
    i.convert_to_continuous_yaw              0x08006cdc   Section        0  mpu6050_driver.o(i.convert_to_continuous_yaw)
    i.dmp_enable_6x_lp_quat                  0x08006d3c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    i.dmp_enable_feature                     0x08006d78   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    i.dmp_enable_gyro_cal                    0x08006f50   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    i.dmp_enable_lp_quat                     0x08006f8c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    i.dmp_load_motion_driver_firmware        0x08006fc8   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    i.dmp_read_fifo                          0x08006fdc   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    i.dmp_set_accel_bias                     0x08007144   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    i.dmp_set_fifo_rate                      0x0800721c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    i.dmp_set_gyro_bias                      0x08007280   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    i.dmp_set_orientation                    0x0800734c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    i.dmp_set_shake_reject_thresh            0x08007458   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    i.dmp_set_shake_reject_time              0x08007488   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    i.dmp_set_shake_reject_timeout           0x080074a8   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    i.dmp_set_tap_axes                       0x080074c8   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    i.dmp_set_tap_count                      0x08007508   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    i.dmp_set_tap_thresh                     0x0800752c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    i.dmp_set_tap_time                       0x0800768c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    i.dmp_set_tap_time_multi                 0x080076ac   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    i.ebtn_init                              0x080076cc   Section        0  ebtn.o(i.ebtn_init)
    i.ebtn_process                           0x08007710   Section        0  ebtn.o(i.ebtn_process)
    i.ebtn_process_btn                       0x08007774   Section        0  ebtn.o(i.ebtn_process_btn)
    ebtn_process_btn                         0x08007775   Thumb Code    46  ebtn.o(i.ebtn_process_btn)
    i.ebtn_process_btn_combo                 0x080077a4   Section        0  ebtn.o(i.ebtn_process_btn_combo)
    ebtn_process_btn_combo                   0x080077a5   Thumb Code   172  ebtn.o(i.ebtn_process_btn_combo)
    i.ebtn_process_with_curr_state           0x08007850   Section        0  ebtn.o(i.ebtn_process_with_curr_state)
    i.ebtn_set_config                        0x080079a8   Section        0  ebtn.o(i.ebtn_set_config)
    i.fabs                                   0x080079b4   Section        0  fabs.o(i.fabs)
    i.get_accel_prod_shift                   0x080079cc   Section        0  inv_mpu.o(i.get_accel_prod_shift)
    get_accel_prod_shift                     0x080079cd   Thumb Code   164  inv_mpu.o(i.get_accel_prod_shift)
    i.get_st_biases                          0x08007a80   Section        0  inv_mpu.o(i.get_st_biases)
    get_st_biases                            0x08007a81   Thumb Code   784  inv_mpu.o(i.get_st_biases)
    i.gyro_self_test                         0x08007d94   Section        0  inv_mpu.o(i.gyro_self_test)
    gyro_self_test                           0x08007d95   Thumb Code   190  inv_mpu.o(i.gyro_self_test)
    i.inv_orientation_matrix_to_scalar       0x08007e68   Section        0  inv_mpu.o(i.inv_orientation_matrix_to_scalar)
    i.inv_row_2_scale                        0x08007e8a   Section        0  inv_mpu.o(i.inv_row_2_scale)
    i.main                                   0x08007ed2   Section        0  main.o(i.main)
    i.mget_ms                                0x08007f0c   Section        0  inv_mpu.o(i.mget_ms)
    i.mpu_configure_fifo                     0x08007f10   Section        0  inv_mpu.o(i.mpu_configure_fifo)
    i.mpu_dmp_get_data                       0x08007f64   Section        0  inv_mpu.o(i.mpu_dmp_get_data)
    i.mpu_dmp_init                           0x080080c8   Section        0  inv_mpu.o(i.mpu_dmp_init)
    i.mpu_get_accel_fsr                      0x08008174   Section        0  inv_mpu.o(i.mpu_get_accel_fsr)
    i.mpu_get_accel_sens                     0x080081b0   Section        0  inv_mpu.o(i.mpu_get_accel_sens)
    i.mpu_get_gyro_fsr                       0x080081f4   Section        0  inv_mpu.o(i.mpu_get_gyro_fsr)
    i.mpu_get_gyro_sens                      0x08008228   Section        0  inv_mpu.o(i.mpu_get_gyro_sens)
    i.mpu_get_lpf                            0x08008274   Section        0  inv_mpu.o(i.mpu_get_lpf)
    i.mpu_get_sample_rate                    0x080082ac   Section        0  inv_mpu.o(i.mpu_get_sample_rate)
    i.mpu_init                               0x080082c8   Section        0  inv_mpu.o(i.mpu_init)
    i.mpu_load_firmware                      0x080083ec   Section        0  inv_mpu.o(i.mpu_load_firmware)
    i.mpu_lp_accel_mode                      0x0800848c   Section        0  inv_mpu.o(i.mpu_lp_accel_mode)
    i.mpu_read_fifo_stream                   0x08008544   Section        0  inv_mpu.o(i.mpu_read_fifo_stream)
    i.mpu_read_mem                           0x080085d4   Section        0  inv_mpu.o(i.mpu_read_mem)
    i.mpu_reset_fifo                         0x08008628   Section        0  inv_mpu.o(i.mpu_reset_fifo)
    i.mpu_run_self_test                      0x08008788   Section        0  inv_mpu.o(i.mpu_run_self_test)
    i.mpu_set_accel_fsr                      0x08008878   Section        0  inv_mpu.o(i.mpu_set_accel_fsr)
    i.mpu_set_bypass                         0x080088e0   Section        0  inv_mpu.o(i.mpu_set_bypass)
    i.mpu_set_dmp_state                      0x080089bc   Section        0  inv_mpu.o(i.mpu_set_dmp_state)
    i.mpu_set_gyro_fsr                       0x08008a34   Section        0  inv_mpu.o(i.mpu_set_gyro_fsr)
    i.mpu_set_int_latched                    0x08008aa0   Section        0  inv_mpu.o(i.mpu_set_int_latched)
    i.mpu_set_lpf                            0x08008b04   Section        0  inv_mpu.o(i.mpu_set_lpf)
    i.mpu_set_sample_rate                    0x08008b70   Section        0  inv_mpu.o(i.mpu_set_sample_rate)
    i.mpu_set_sensors                        0x08008bf0   Section        0  inv_mpu.o(i.mpu_set_sensors)
    i.mpu_write_mem                          0x08008ca8   Section        0  inv_mpu.o(i.mpu_write_mem)
    i.my_get_key_state                       0x08008cfc   Section        0  key_driver.o(i.my_get_key_state)
    i.my_handle_key_event                    0x08008d18   Section        0  key_app.o(i.my_handle_key_event)
    i.pid_calculate_positional               0x08008d68   Section        0  pid.o(i.pid_calculate_positional)
    i.pid_constrain                          0x08008dce   Section        0  pid.o(i.pid_constrain)
    i.pid_init                               0x08008df0   Section        0  pid.o(i.pid_init)
    i.pid_out_limit                          0x08008e20   Section        0  pid.o(i.pid_out_limit)
    pid_out_limit                            0x08008e21   Thumb Code    38  pid.o(i.pid_out_limit)
    i.pid_reset                              0x08008e48   Section        0  pid.o(i.pid_reset)
    i.pid_set_target                         0x08008e70   Section        0  pid.o(i.pid_set_target)
    i.prv_process_btn                        0x08008e78   Section        0  ebtn.o(i.prv_process_btn)
    prv_process_btn                          0x08008e79   Thumb Code   328  ebtn.o(i.prv_process_btn)
    i.rt_ringbuffer_data_len                 0x08008fc4   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_init                     0x08008ff4   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x0800901a   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x0800908c   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    i.run_self_test                          0x080090ac   Section        0  inv_mpu.o(i.run_self_test)
    i.set_int_enable                         0x08009134   Section        0  inv_mpu.o(i.set_int_enable)
    set_int_enable                           0x08009135   Thumb Code    84  inv_mpu.o(i.set_int_enable)
    i.sqrt                                   0x0800918c   Section        0  sqrt.o(i.sqrt)
    locale$$code                             0x080091fc   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08009228   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$basic                              0x08009254   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x08009254   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x0800926c   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x0800926c   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x080092d0   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x080092d0   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x080092e1   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcmpinf                            0x08009420   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08009420   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08009438   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08009438   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x0800943f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x080096e8   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x080096e8   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dfltu                              0x08009760   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08009760   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x08009788   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08009788   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080098dc   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x080098dc   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08009978   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08009978   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x08009984   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x08009984   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x0800999c   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x0800999c   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x08009b34   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08009b34   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08009b45   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08009d08   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08009d08   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08009d5e   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08009d5e   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08009dea   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08009dea   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x08009df4   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x08009df4   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x08009dfe   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08009dfe   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08009e02   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08009e02   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x08009e06   Section        8  stm32f4xx_hal_dma.o(.constdata)
    x$fpl$usenofp                            0x08009e06   Section        0  usenofp.o(x$fpl$usenofp)
    flagBitshiftOffset                       0x08009e06   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08009e0e   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08009e1e   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08009e26   Section       27  inv_mpu.o(.constdata)
    .constdata                               0x08009e42   Section       12  inv_mpu.o(.constdata)
    .constdata                               0x08009e50   Section       40  inv_mpu.o(.constdata)
    .constdata                               0x08009e78   Section     3062  inv_mpu_dmp_motion_driver.o(.constdata)
    dmp_memory                               0x08009e78   Data        3062  inv_mpu_dmp_motion_driver.o(.constdata)
    .constdata                               0x0800aa6e   Section       14  key_driver.o(.constdata)
    defaul_ebtn_param                        0x0800aa6e   Data          14  key_driver.o(.constdata)
    .constdata                               0x0800aa80   Section       80  asin.o(.constdata)
    pS                                       0x0800aa80   Data          48  asin.o(.constdata)
    qS                                       0x0800aab0   Data          32  asin.o(.constdata)
    .constdata                               0x0800aad0   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x0800aad0   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x0800aad8   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x0800aad8   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800aaec   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0800ab00   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800ab00   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800ab18   Section      152  atan.o(.constdata)
    atanhi                                   0x0800ab18   Data          32  atan.o(.constdata)
    atanlo                                   0x0800ab38   Data          32  atan.o(.constdata)
    aTodd                                    0x0800ab58   Data          40  atan.o(.constdata)
    aTeven                                   0x0800ab80   Data          48  atan.o(.constdata)
    .constdata                               0x0800abb0   Section        8  qnan.o(.constdata)
    .constdata                               0x0800abb8   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0800abb8   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800abcb   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800abe0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800abe0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800ac1c   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x0800ac94   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800ac98   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800aca0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800acac   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800acae   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800acaf   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800acb0   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800acb0   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0800acb4   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800acbc   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800adc0   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section     2079  oled.o(.data)
    F6x8                                     0x20000010   Data         552  oled.o(.data)
    F8X16                                    0x20000238   Data        1504  oled.o(.data)
    .data                                    0x20000830   Section       53  inv_mpu.o(.data)
    st                                       0x20000830   Data          44  inv_mpu.o(.data)
    gyro_orientation                         0x2000085c   Data           9  inv_mpu.o(.data)
    .data                                    0x20000868   Section       28  key_driver.o(.data)
    static_buttons                           0x20000868   Data          28  key_driver.o(.data)
    .data                                    0x20000884   Section        1  led_driver.o(.data)
    led_temp_old                             0x20000884   Data           1  led_driver.o(.data)
    .data                                    0x20000888   Section       12  mpu6050_driver.o(.data)
    g_is_yaw_initialized                     0x20000888   Data           1  mpu6050_driver.o(.data)
    g_last_yaw                               0x2000088c   Data           4  mpu6050_driver.o(.data)
    g_revolution_count                       0x20000890   Data           4  mpu6050_driver.o(.data)
    .data                                    0x20000894   Section       40  gray_app.o(.data)
    .data                                    0x200008bc   Section        1  led_app.o(.data)
    .data                                    0x200008c0   Section       88  pid_app.o(.data)
    .data                                    0x20000918   Section       12  mpu6050_app.o(.data)
    .data                                    0x20000924   Section       28  scheduler.o(.data)
    scheduler_task                           0x20000928   Data          24  scheduler.o(.data)
    .data                                    0x20000940   Section       24  scheduler_task.o(.data)
    .bss                                     0x20000958   Section      168  i2c.o(.bss)
    .bss                                     0x20000a00   Section      288  tim.o(.bss)
    .bss                                     0x20000b20   Section      240  usart.o(.bss)
    .bss                                     0x20000c10   Section       52  ebtn.o(.bss)
    ebtn_default                             0x20000c10   Data          52  ebtn.o(.bss)
    .bss                                     0x20000c44   Section       16  inv_mpu_dmp_motion_driver.o(.bss)
    dmp                                      0x20000c44   Data          16  inv_mpu_dmp_motion_driver.o(.bss)
    .bss                                     0x20000c54   Section      140  uart_driver.o(.bss)
    .bss                                     0x20000ce0   Section      128  uart_driver.o(.bss)
    .bss                                     0x20000d60   Section       32  encoder_app.o(.bss)
    .bss                                     0x20000d80   Section       72  motor_app.o(.bss)
    .bss                                     0x20000dc8   Section      240  pid_app.o(.bss)
    .bss                                     0x20000eb8   Section       96  libspace.o(.bss)
    HEAP                                     0x20000f18   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000f18   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20001118   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20001118   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20001518   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x0800023d   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000243   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000249   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800024f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000255   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800025b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000261   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800026b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000271   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000277   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800027d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000283   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000289   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800028f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000295   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800029b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080002a1   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080002a7   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080002b1   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002b7   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002bd   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002c3   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002c9   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002cd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002d9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080002d9   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002e5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002ef   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002f1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002f3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002f5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002f5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002f5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002fb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002ff   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000307   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000309   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000309   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800030d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000315   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x0800032f   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x08000331   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000355   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000355   Thumb Code   238  lludivv7m.o(.text)
    __aeabi_ldivmod                          0x08000443   Thumb Code     0  llsdiv.o(.text)
    _ll_sdiv                                 0x08000443   Thumb Code    72  llsdiv.o(.text)
    vsnprintf                                0x0800048d   Thumb Code    48  vsnprintf.o(.text)
    memcmp                                   0x080004c1   Thumb Code    88  memcmp.o(.text)
    __aeabi_memcpy                           0x08000519   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000519   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x0800057f   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x080005a3   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080005a3   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080005a3   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080005eb   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memset                           0x08000607   Thumb Code    16  aeabi_memset.o(.text)
    __aeabi_memclr                           0x08000617   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000617   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x0800061b   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_memclr4                          0x0800065b   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800065b   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800065b   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800065f   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080006a9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080006ab   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080006ad   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x080006af   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080006b9   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x080006c5   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080006f1   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000713   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000725   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08000737   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000789   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000801   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000833   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000859   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000863   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000875   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000931   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x080009ad   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x080009ef   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000a07   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000a1d   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000a73   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000a8f   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000a9b   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x08000ab1   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __aeabi_errno_addr                       0x08000c39   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000c39   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000c39   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000c41   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000ccb   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000d7d   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000f2f   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x0800119d   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x08001499   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080014ad   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080014bd   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x080014c5   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x080014d9   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x080014e9   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x080014f1   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x08001531   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001531   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001531   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08001539   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001585   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001595   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x0800159d   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x0800161d   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001701   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08001715   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x08001795   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080017a1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080017a1   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080017a3   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x080017a3   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080017e1   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001827   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001887   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001bbf   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001c9b   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001cc5   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001cef   Thumb Code   580  btod.o(CL$$btod_mult_common)
    Angle_PID_control                        0x08001f35   Thumb Code    92  pid_app.o(i.Angle_PID_control)
    BusFault_Handler                         0x08001f9d   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    Car_State_Update                         0x08001fa1   Thumb Code   240  scheduler_task.o(i.Car_State_Update)
    DMA2_Stream2_IRQHandler                  0x080020d1   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08002181   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Ebtn_Init                                0x08002185   Thumb Code    40  key_driver.o(i.Ebtn_Init)
    Encoder_Driver_Init                      0x080021b9   Thumb Code    38  encoder_driver.o(i.Encoder_Driver_Init)
    Encoder_Driver_Update                    0x080021e5   Thumb Code    72  encoder_driver.o(i.Encoder_Driver_Update)
    Encoder_Init                             0x08002239   Thumb Code    28  encoder_app.o(i.Encoder_Init)
    Encoder_Task                             0x08002261   Thumb Code    20  encoder_app.o(i.Encoder_Task)
    Error_Handler                            0x08002279   Thumb Code     4  main.o(i.Error_Handler)
    Gray_Init                                0x0800227d   Thumb Code     2  gray_app.o(i.Gray_Init)
    Gray_Task                                0x08002281   Thumb Code    82  gray_app.o(i.Gray_Task)
    HAL_DMA_Abort                            0x080022dd   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x0800236f   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002395   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002535   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002609   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08002679   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x0800269d   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x0800288d   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x08002897   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080028a1   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x080028ad   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Mem_Read                         0x08002a35   Thumb Code   502  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read)
    HAL_I2C_Mem_Write                        0x08002c39   Thumb Code   294  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x08002d69   Thumb Code   208  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08002e51   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002e61   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002e95   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002ed5   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002f05   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002f21   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002f61   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002f85   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x080030b9   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080030d9   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080030f9   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003159   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080034c5   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x080034ed   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x080034ef   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x080034f1   Thumb Code    84  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08003545   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080035d5   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003631   Thumb Code    76  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x08003685   Thumb Code   100  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08003705   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x080037e1   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08003885   Thumb Code   142  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08003929   Thumb Code   142  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x080039b7   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x080039b9   Thumb Code   304  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08003ae9   Thumb Code    72  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08003b3d   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08003b3f   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08003c0b   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003c65   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x08003c67   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x08003c69   Thumb Code   172  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08003d31   Thumb Code   228  scheduler_task.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x08003e29   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08003e2b   Thumb Code    74  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08003e75   Thumb Code    60  uart_driver.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08003ec1   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x08003f31   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003f35   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080041b5   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004219   Thumb Code   226  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08004319   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x0800431b   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x0800431d   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080043bd   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080043bf   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    IIC_Ack                                  0x08004801   Thumb Code    60  iic.o(i.IIC_Ack)
    IIC_GPIO_Init                            0x0800484d   Thumb Code    50  iic.o(i.IIC_GPIO_Init)
    IIC_Get_Digtal                           0x08004889   Thumb Code    20  hardware_iic.o(i.IIC_Get_Digtal)
    IIC_NAck                                 0x0800489d   Thumb Code    50  iic.o(i.IIC_NAck)
    IIC_ReadBytes                            0x080048d5   Thumb Code    32  hardware_iic.o(i.IIC_ReadBytes)
    IIC_Read_Byte                            0x080048f9   Thumb Code    84  iic.o(i.IIC_Read_Byte)
    IIC_Send_Byte                            0x08004951   Thumb Code    86  iic.o(i.IIC_Send_Byte)
    IIC_Start                                0x080049ad   Thumb Code    60  iic.o(i.IIC_Start)
    IIC_Stop                                 0x080049ed   Thumb Code    42  iic.o(i.IIC_Stop)
    IIC_Wait_Ack                             0x08004a1d   Thumb Code    66  iic.o(i.IIC_Wait_Ack)
    Key_Init                                 0x08004a65   Thumb Code     4  key_app.o(i.Key_Init)
    Key_Task                                 0x08004a69   Thumb Code    14  key_app.o(i.Key_Task)
    Led_Display                              0x08004a79   Thumb Code    32  led_driver.o(i.Led_Display)
    Led_Init                                 0x08004aa1   Thumb Code     6  led_app.o(i.Led_Init)
    Led_Task                                 0x08004aa9   Thumb Code     8  led_app.o(i.Led_Task)
    Line_PID_control                         0x08004ab5   Thumb Code    92  pid_app.o(i.Line_PID_control)
    MPU_Get_Gyro_Offset                      0x08004b1d   Thumb Code    92  mpu6050_driver.o(i.MPU_Get_Gyro_Offset)
    MPU_Get_Gyroscope                        0x08004b79   Thumb Code    50  mpu6050.o(i.MPU_Get_Gyroscope)
    MPU_Init                                 0x08004bab   Thumb Code   118  mpu6050.o(i.MPU_Init)
    MPU_Read_Byte                            0x08004c21   Thumb Code    58  mpu6050.o(i.MPU_Read_Byte)
    MPU_Read_Len                             0x08004c5d   Thumb Code   108  mpu6050.o(i.MPU_Read_Len)
    MPU_Set_Accel_Fsr                        0x08004cc9   Thumb Code    10  mpu6050.o(i.MPU_Set_Accel_Fsr)
    MPU_Set_Gyro_Fsr                         0x08004cd3   Thumb Code    10  mpu6050.o(i.MPU_Set_Gyro_Fsr)
    MPU_Set_LPF                              0x08004cdd   Thumb Code    48  mpu6050.o(i.MPU_Set_LPF)
    MPU_Set_Rate                             0x08004d0d   Thumb Code    46  mpu6050.o(i.MPU_Set_Rate)
    MPU_Write_Byte                           0x08004d3b   Thumb Code    60  mpu6050.o(i.MPU_Write_Byte)
    MPU_Write_Len                            0x08004d77   Thumb Code    82  mpu6050.o(i.MPU_Write_Len)
    MX_DMA_Init                              0x08004dc9   Thumb Code    40  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08004df5   Thumb Code   236  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08004ef1   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_I2C3_Init                             0x08004f31   Thumb Code    50  i2c.o(i.MX_I2C3_Init)
    MX_TIM1_Init                             0x08004f71   Thumb Code   208  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x08005049   Thumb Code    96  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x080050ad   Thumb Code   100  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08005119   Thumb Code   100  tim.o(i.MX_TIM4_Init)
    MX_USART1_UART_Init                      0x08005185   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x080051bd   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MemManage_Handler                        0x080051f5   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    Motor_Brake                              0x080051f7   Thumb Code    68  motor_driver.o(i.Motor_Brake)
    Motor_Config_Init                        0x0800523b   Thumb Code   110  motor_driver.o(i.Motor_Config_Init)
    Motor_Dead_Compensation                  0x080052a9   Thumb Code    30  motor_driver.o(i.Motor_Dead_Compensation)
    Motor_Init                               0x080052c9   Thumb Code    70  motor_app.o(i.Motor_Init)
    Motor_Limit_Speed                        0x0800531d   Thumb Code    18  motor_driver.o(i.Motor_Limit_Speed)
    Motor_Set_Speed                          0x0800532f   Thumb Code   164  motor_driver.o(i.Motor_Set_Speed)
    Mpu6050_Init                             0x080053d3   Thumb Code    14  mpu6050_app.o(i.Mpu6050_Init)
    Mpu6050_Task                             0x080053e1   Thumb Code    30  mpu6050_app.o(i.Mpu6050_Task)
    NMI_Handler                              0x08005405   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08005407   Thumb Code    52  oled.o(i.OLED_Clear)
    OLED_Init                                0x0800543d   Thumb Code    28  oled.o(i.OLED_Init)
    OLED_Set_Pos                             0x0800545d   Thumb Code    34  oled.o(i.OLED_Set_Pos)
    OLED_ShowChar                            0x08005481   Thumb Code   148  oled.o(i.OLED_ShowChar)
    OLED_ShowString                          0x0800551d   Thumb Code    88  oled.o(i.OLED_ShowString)
    OLED_WR_CMD                              0x08005575   Thumb Code    32  oled.o(i.OLED_WR_CMD)
    OLED_WR_DATA                             0x08005599   Thumb Code    32  oled.o(i.OLED_WR_DATA)
    Oled_Init                                0x080055bd   Thumb Code    14  oled_app.o(i.Oled_Init)
    Oled_Printf                              0x080055cb   Thumb Code    50  oled_driver.o(i.Oled_Printf)
    Oled_Task                                0x080055fd   Thumb Code   202  oled_app.o(i.Oled_Task)
    PID_Init                                 0x08005735   Thumb Code   168  pid_app.o(i.PID_Init)
    PID_Task                                 0x080057e9   Thumb Code   172  pid_app.o(i.PID_Task)
    PendSV_Handler                           0x080058b1   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x080058b3   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Scheduler_Init                           0x080058b5   Thumb Code    14  scheduler.o(i.Scheduler_Init)
    Scheduler_Run                            0x080058c9   Thumb Code    56  scheduler.o(i.Scheduler_Run)
    SysTick_Handler                          0x08005905   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08005909   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x0800599d   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    System_Init                              0x080059ad   Thumb Code    56  scheduler_task.o(i.System_Init)
    TIM2_IRQHandler                          0x08005a05   Thumb Code     6  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x08005a11   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08005ae1   Thumb Code    26  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x08005afb   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08005b81   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_Start_Receive_DMA                   0x08006021   Thumb Code   146  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08006135   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08006141   Thumb Code     6  stm32f4xx_it.o(i.USART2_IRQHandler)
    Uart_Init                                0x0800614d   Thumb Code    36  uart_app.o(i.Uart_Init)
    Uart_Printf                              0x08006185   Thumb Code    50  uart_driver.o(i.Uart_Printf)
    UsageFault_Handler                       0x080061b7   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x080061b9   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_asin                            0x08006209   Thumb Code   770  asin.o(i.__hardfp_asin)
    __hardfp_atan                            0x08006569   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_atan2                           0x08006841   Thumb Code   448  atan2.o(i.__hardfp_atan2)
    __kernel_poly                            0x08006a41   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x08006b39   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x08006b4d   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08006b61   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x08006b81   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x08006ba1   Thumb Code    14  __printf_wp.o(i._is_digit)
    atan                                     0x08006c41   Thumb Code    16  atan.o(i.atan)
    convert_to_continuous_yaw                0x08006cdd   Thumb Code    80  mpu6050_driver.o(i.convert_to_continuous_yaw)
    dmp_enable_6x_lp_quat                    0x08006d3d   Thumb Code    58  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    dmp_enable_feature                       0x08006d79   Thumb Code   464  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    dmp_enable_gyro_cal                      0x08006f51   Thumb Code    34  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    dmp_enable_lp_quat                       0x08006f8d   Thumb Code    58  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    dmp_load_motion_driver_firmware          0x08006fc9   Thumb Code    16  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    dmp_read_fifo                            0x08006fdd   Thumb Code   356  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    dmp_set_accel_bias                       0x08007145   Thumb Code   210  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    dmp_set_fifo_rate                        0x0800721d   Thumb Code    82  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    dmp_set_gyro_bias                        0x08007281   Thumb Code   196  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    dmp_set_orientation                      0x0800734d   Thumb Code   246  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    dmp_set_shake_reject_thresh              0x08007459   Thumb Code    48  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    dmp_set_shake_reject_time                0x08007489   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    dmp_set_shake_reject_timeout             0x080074a9   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    dmp_set_tap_axes                         0x080074c9   Thumb Code    64  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    dmp_set_tap_count                        0x08007509   Thumb Code    34  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    dmp_set_tap_thresh                       0x0800752d   Thumb Code   314  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    dmp_set_tap_time                         0x0800768d   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    dmp_set_tap_time_multi                   0x080076ad   Thumb Code    32  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    ebtn_init                                0x080076cd   Thumb Code    64  ebtn.o(i.ebtn_init)
    ebtn_process                             0x08007711   Thumb Code    94  ebtn.o(i.ebtn_process)
    ebtn_process_with_curr_state             0x08007851   Thumb Code   340  ebtn.o(i.ebtn_process_with_curr_state)
    ebtn_set_config                          0x080079a9   Thumb Code     8  ebtn.o(i.ebtn_set_config)
    fabs                                     0x080079b5   Thumb Code    24  fabs.o(i.fabs)
    inv_orientation_matrix_to_scalar         0x08007e69   Thumb Code    34  inv_mpu.o(i.inv_orientation_matrix_to_scalar)
    inv_row_2_scale                          0x08007e8b   Thumb Code    72  inv_mpu.o(i.inv_row_2_scale)
    main                                     0x08007ed3   Thumb Code    58  main.o(i.main)
    mget_ms                                  0x08007f0d   Thumb Code     2  inv_mpu.o(i.mget_ms)
    mpu_configure_fifo                       0x08007f11   Thumb Code    78  inv_mpu.o(i.mpu_configure_fifo)
    mpu_dmp_get_data                         0x08007f65   Thumb Code   344  inv_mpu.o(i.mpu_dmp_get_data)
    mpu_dmp_init                             0x080080c9   Thumb Code   168  inv_mpu.o(i.mpu_dmp_init)
    mpu_get_accel_fsr                        0x08008175   Thumb Code    54  inv_mpu.o(i.mpu_get_accel_fsr)
    mpu_get_accel_sens                       0x080081b1   Thumb Code    62  inv_mpu.o(i.mpu_get_accel_sens)
    mpu_get_gyro_fsr                         0x080081f5   Thumb Code    48  inv_mpu.o(i.mpu_get_gyro_fsr)
    mpu_get_gyro_sens                        0x08008229   Thumb Code    54  inv_mpu.o(i.mpu_get_gyro_sens)
    mpu_get_lpf                              0x08008275   Thumb Code    52  inv_mpu.o(i.mpu_get_lpf)
    mpu_get_sample_rate                      0x080082ad   Thumb Code    22  inv_mpu.o(i.mpu_get_sample_rate)
    mpu_init                                 0x080082c9   Thumb Code   288  inv_mpu.o(i.mpu_init)
    mpu_load_firmware                        0x080083ed   Thumb Code   154  inv_mpu.o(i.mpu_load_firmware)
    mpu_lp_accel_mode                        0x0800848d   Thumb Code   178  inv_mpu.o(i.mpu_lp_accel_mode)
    mpu_read_fifo_stream                     0x08008545   Thumb Code   140  inv_mpu.o(i.mpu_read_fifo_stream)
    mpu_read_mem                             0x080085d5   Thumb Code    80  inv_mpu.o(i.mpu_read_mem)
    mpu_reset_fifo                           0x08008629   Thumb Code   346  inv_mpu.o(i.mpu_reset_fifo)
    mpu_run_self_test                        0x08008789   Thumb Code   234  inv_mpu.o(i.mpu_run_self_test)
    mpu_set_accel_fsr                        0x08008879   Thumb Code    98  inv_mpu.o(i.mpu_set_accel_fsr)
    mpu_set_bypass                           0x080088e1   Thumb Code   214  inv_mpu.o(i.mpu_set_bypass)
    mpu_set_dmp_state                        0x080089bd   Thumb Code   114  inv_mpu.o(i.mpu_set_dmp_state)
    mpu_set_gyro_fsr                         0x08008a35   Thumb Code   104  inv_mpu.o(i.mpu_set_gyro_fsr)
    mpu_set_int_latched                      0x08008aa1   Thumb Code    94  inv_mpu.o(i.mpu_set_int_latched)
    mpu_set_lpf                              0x08008b05   Thumb Code   102  inv_mpu.o(i.mpu_set_lpf)
    mpu_set_sample_rate                      0x08008b71   Thumb Code   124  inv_mpu.o(i.mpu_set_sample_rate)
    mpu_set_sensors                          0x08008bf1   Thumb Code   178  inv_mpu.o(i.mpu_set_sensors)
    mpu_write_mem                            0x08008ca9   Thumb Code    80  inv_mpu.o(i.mpu_write_mem)
    my_get_key_state                         0x08008cfd   Thumb Code    24  key_driver.o(i.my_get_key_state)
    my_handle_key_event                      0x08008d19   Thumb Code    54  key_app.o(i.my_handle_key_event)
    pid_calculate_positional                 0x08008d69   Thumb Code   102  pid.o(i.pid_calculate_positional)
    pid_constrain                            0x08008dcf   Thumb Code    32  pid.o(i.pid_constrain)
    pid_init                                 0x08008df1   Thumb Code    42  pid.o(i.pid_init)
    pid_reset                                0x08008e49   Thumb Code    34  pid.o(i.pid_reset)
    pid_set_target                           0x08008e71   Thumb Code     6  pid.o(i.pid_set_target)
    rt_ringbuffer_data_len                   0x08008fc5   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_init                       0x08008ff5   Thumb Code    38  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x0800901b   Thumb Code   114  ringbuffer.o(i.rt_ringbuffer_put)
    rt_ringbuffer_status                     0x0800908d   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    run_self_test                            0x080090ad   Thumb Code   136  inv_mpu.o(i.run_self_test)
    sqrt                                     0x0800918d   Thumb Code   110  sqrt.o(i.sqrt)
    _get_lc_numeric                          0x080091fd   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08009229   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_dneg                             0x08009255   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x08009255   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x0800925b   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x0800925b   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x08009261   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x08009267   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x0800926d   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x0800926d   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x080092d1   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x080092d1   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcmp_Inf                           0x08009421   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08009439   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08009439   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x080096e9   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x080096e9   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_ui2d                             0x08009761   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08009761   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x08009789   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08009789   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080098dd   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08009979   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x08009985   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08009985   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x0800999d   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x08009b35   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08009b35   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08009d09   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08009d09   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08009d5f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08009deb   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x08009df3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x08009df3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x08009df5   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x08009dff   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08009e03   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08009e06   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08009e0e   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08009e1e   Data           8  system_stm32f4xx.o(.constdata)
    reg                                      0x08009e26   Data          27  inv_mpu.o(.constdata)
    hw                                       0x08009e42   Data          12  inv_mpu.o(.constdata)
    test                                     0x08009e50   Data          40  inv_mpu.o(.constdata)
    __mathlib_zero                           0x0800abb0   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0800ac74   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800ac94   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x0800acbd   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    CMD_Data                                 0x20000818   Data          23  oled.o(.data)
    Digtal                                   0x20000894   Data           1  gray_app.o(.data)
    g_line_position_error                    0x20000898   Data           4  gray_app.o(.data)
    gray_weights                             0x2000089c   Data          32  gray_app.o(.data)
    led_state                                0x200008bc   Data           1  led_app.o(.data)
    pid_running                              0x200008c0   Data           1  pid_app.o(.data)
    pid_control_mode                         0x200008c1   Data           1  pid_app.o(.data)
    basic_speed                              0x200008c4   Data           4  pid_app.o(.data)
    pid_params_left                          0x200008c8   Data          20  pid_app.o(.data)
    pid_params_right                         0x200008dc   Data          20  pid_app.o(.data)
    pid_params_line                          0x200008f0   Data          20  pid_app.o(.data)
    pid_params_angle                         0x20000904   Data          20  pid_app.o(.data)
    Pitch                                    0x20000918   Data           4  mpu6050_app.o(.data)
    Roll                                     0x2000091c   Data           4  mpu6050_app.o(.data)
    Yaw                                      0x20000920   Data           4  mpu6050_app.o(.data)
    task_num                                 0x20000924   Data           1  scheduler.o(.data)
    point_count                              0x20000940   Data           1  scheduler_task.o(.data)
    system_mode                              0x20000941   Data           1  scheduler_task.o(.data)
    circle_count                             0x20000942   Data           1  scheduler_task.o(.data)
    measure_timer5ms                         0x20000943   Data           1  scheduler_task.o(.data)
    key_timer10ms                            0x20000944   Data           1  scheduler_task.o(.data)
    output_ff_flag                           0x20000945   Data           1  scheduler_task.o(.data)
    intput_ff_flag                           0x20000946   Data           1  scheduler_task.o(.data)
    distance                                 0x20000948   Data           4  scheduler_task.o(.data)
    intput_timer500ms                        0x2000094c   Data           4  scheduler_task.o(.data)
    output_timer500ms                        0x20000950   Data           4  scheduler_task.o(.data)
    led_timer500ms                           0x20000954   Data           4  scheduler_task.o(.data)
    hi2c1                                    0x20000958   Data          84  i2c.o(.bss)
    hi2c3                                    0x200009ac   Data          84  i2c.o(.bss)
    htim1                                    0x20000a00   Data          72  tim.o(.bss)
    htim2                                    0x20000a48   Data          72  tim.o(.bss)
    htim3                                    0x20000a90   Data          72  tim.o(.bss)
    htim4                                    0x20000ad8   Data          72  tim.o(.bss)
    huart1                                   0x20000b20   Data          72  usart.o(.bss)
    huart2                                   0x20000b68   Data          72  usart.o(.bss)
    hdma_usart1_rx                           0x20000bb0   Data          96  usart.o(.bss)
    uart_rx_dma_buffer                       0x20000c54   Data         128  uart_driver.o(.bss)
    ring_buffer                              0x20000cd4   Data          12  uart_driver.o(.bss)
    ring_buffer_input                        0x20000ce0   Data         128  uart_driver.o(.bss)
    left_encoder                             0x20000d60   Data          16  encoder_app.o(.bss)
    right_encoder                            0x20000d70   Data          16  encoder_app.o(.bss)
    left_motor                               0x20000d80   Data          36  motor_app.o(.bss)
    right_motor                              0x20000da4   Data          36  motor_app.o(.bss)
    pid_speed_left                           0x20000dc8   Data          60  pid_app.o(.bss)
    pid_speed_right                          0x20000e04   Data          60  pid_app.o(.bss)
    pid_line                                 0x20000e40   Data          60  pid_app.o(.bss)
    pid_angle                                0x20000e7c   Data          60  pid_app.o(.bss)
    __libspace_start                         0x20000eb8   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000f18   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000b718, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x0000b368])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000adc0, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5588  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         6047    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         6045    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         6049    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         5728    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         5717    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000242   0x08000242   0x00000006   Code   RO         5719    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000248   0x08000248   0x00000006   Code   RO         5724    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         5725    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000254   0x08000254   0x00000006   Code   RO         5726    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         5727    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000260   0x08000260   0x0000000a   Code   RO         5732    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         5721    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000270   0x08000270   0x00000006   Code   RO         5722    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000276   0x08000276   0x00000006   Code   RO         5723    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         5720    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000282   0x08000282   0x00000006   Code   RO         5718    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000288   0x08000288   0x00000006   Code   RO         5729    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800028e   0x0800028e   0x00000006   Code   RO         5730    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000294   0x08000294   0x00000006   Code   RO         5731    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800029a   0x0800029a   0x00000006   Code   RO         5736    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080002a0   0x080002a0   0x00000006   Code   RO         5737    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080002a6   0x080002a6   0x0000000a   Code   RO         5733    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080002b0   0x080002b0   0x00000006   Code   RO         5715    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002b6   0x080002b6   0x00000006   Code   RO         5716    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002bc   0x080002bc   0x00000006   Code   RO         5734    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002c2   0x080002c2   0x00000006   Code   RO         5735    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         5847    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002cc   0x080002cc   0x00000002   Code   RO         5909    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002ce   0x080002ce   0x00000004   Code   RO         5925    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         5928    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         5931    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         5933    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         5935    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000006   Code   RO         5936    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002d8   0x080002d8   0x00000000   Code   RO         5938    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002d8   0x080002d8   0x0000000c   Code   RO         5939    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002e4   0x080002e4   0x00000000   Code   RO         5940    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002e4   0x080002e4   0x00000000   Code   RO         5942    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002e4   0x080002e4   0x0000000a   Code   RO         5943    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5944    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5946    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5948    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5950    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5952    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5954    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5956    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5958    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5962    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5964    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5966    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000000   Code   RO         5968    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ee   0x080002ee   0x00000002   Code   RO         5969    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002f0   0x080002f0   0x00000002   Code   RO         6000    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         6026    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         6028    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         6030    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         6033    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         6036    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         6038    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000000   Code   RO         6041    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002f2   0x080002f2   0x00000002   Code   RO         6042    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002f4   0x080002f4   0x00000000   Code   RO         5638    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002f4   0x080002f4   0x00000000   Code   RO         5811    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002f4   0x080002f4   0x00000006   Code   RO         5823    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         5813    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002fa   0x080002fa   0x00000004   Code   RO         5814    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002fe   0x080002fe   0x00000000   Code   RO         5816    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002fe   0x080002fe   0x00000008   Code   RO         5817    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000306   0x08000306   0x00000002   Code   RO         5912    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000308   0x08000308   0x00000000   Code   RO         5973    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000308   0x08000308   0x00000004   Code   RO         5974    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800030c   0x0800030c   0x00000006   Code   RO         5975    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000312   0x08000312   0x00000002   PAD
    0x08000314   0x08000314   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000354   0x08000354   0x000000ee   Code   RO         5568    .text               c_w.l(lludivv7m.o)
    0x08000442   0x08000442   0x00000048   Code   RO         5570    .text               c_w.l(llsdiv.o)
    0x0800048a   0x0800048a   0x00000002   PAD
    0x0800048c   0x0800048c   0x00000034   Code   RO         5572    .text               c_w.l(vsnprintf.o)
    0x080004c0   0x080004c0   0x00000058   Code   RO         5574    .text               c_w.l(memcmp.o)
    0x08000518   0x08000518   0x0000008a   Code   RO         5576    .text               c_w.l(rt_memcpy_v6.o)
    0x080005a2   0x080005a2   0x00000064   Code   RO         5578    .text               c_w.l(rt_memcpy_w.o)
    0x08000606   0x08000606   0x00000010   Code   RO         5580    .text               c_w.l(aeabi_memset.o)
    0x08000616   0x08000616   0x00000044   Code   RO         5582    .text               c_w.l(rt_memclr.o)
    0x0800065a   0x0800065a   0x0000004e   Code   RO         5584    .text               c_w.l(rt_memclr_w.o)
    0x080006a8   0x080006a8   0x00000006   Code   RO         5586    .text               c_w.l(heapauxi.o)
    0x080006ae   0x080006ae   0x00000016   Code   RO         5643    .text               c_w.l(_rserrno.o)
    0x080006c4   0x080006c4   0x0000004e   Code   RO         5647    .text               c_w.l(_printf_pad.o)
    0x08000712   0x08000712   0x00000024   Code   RO         5649    .text               c_w.l(_printf_truncate.o)
    0x08000736   0x08000736   0x00000052   Code   RO         5651    .text               c_w.l(_printf_str.o)
    0x08000788   0x08000788   0x00000078   Code   RO         5653    .text               c_w.l(_printf_dec.o)
    0x08000800   0x08000800   0x00000028   Code   RO         5655    .text               c_w.l(_printf_charcount.o)
    0x08000828   0x08000828   0x00000030   Code   RO         5657    .text               c_w.l(_printf_char_common.o)
    0x08000858   0x08000858   0x0000000a   Code   RO         5659    .text               c_w.l(_sputc.o)
    0x08000862   0x08000862   0x00000010   Code   RO         5661    .text               c_w.l(_snputc.o)
    0x08000872   0x08000872   0x00000002   PAD
    0x08000874   0x08000874   0x000000bc   Code   RO         5663    .text               c_w.l(_printf_wctomb.o)
    0x08000930   0x08000930   0x0000007c   Code   RO         5666    .text               c_w.l(_printf_longlong_dec.o)
    0x080009ac   0x080009ac   0x00000070   Code   RO         5672    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000a1c   0x08000a1c   0x00000094   Code   RO         5692    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000ab0   0x08000ab0   0x00000188   Code   RO         5712    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000c38   0x08000c38   0x00000008   Code   RO         5830    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000c40   0x08000c40   0x0000008a   Code   RO         5832    .text               c_w.l(lludiv10.o)
    0x08000cca   0x08000cca   0x000000b2   Code   RO         5834    .text               c_w.l(_printf_intcommon.o)
    0x08000d7c   0x08000d7c   0x0000041e   Code   RO         5836    .text               c_w.l(_printf_fp_dec.o)
    0x0800119a   0x0800119a   0x00000002   PAD
    0x0800119c   0x0800119c   0x000002fc   Code   RO         5838    .text               c_w.l(_printf_fp_hex.o)
    0x08001498   0x08001498   0x0000002c   Code   RO         5843    .text               c_w.l(_printf_char.o)
    0x080014c4   0x080014c4   0x0000002c   Code   RO         5845    .text               c_w.l(_printf_wchar.o)
    0x080014f0   0x080014f0   0x00000040   Code   RO         5848    .text               c_w.l(_wcrtomb.o)
    0x08001530   0x08001530   0x00000008   Code   RO         5856    .text               c_w.l(libspace.o)
    0x08001538   0x08001538   0x0000004a   Code   RO         5859    .text               c_w.l(sys_stackheap_outer.o)
    0x08001582   0x08001582   0x00000002   PAD
    0x08001584   0x08001584   0x00000010   Code   RO         5861    .text               c_w.l(rt_ctype_table.o)
    0x08001594   0x08001594   0x00000008   Code   RO         5866    .text               c_w.l(rt_locale_intlibspace.o)
    0x0800159c   0x0800159c   0x00000080   Code   RO         5868    .text               c_w.l(_printf_fp_infnan.o)
    0x0800161c   0x0800161c   0x000000e4   Code   RO         5870    .text               c_w.l(bigflt0.o)
    0x08001700   0x08001700   0x00000012   Code   RO         5898    .text               c_w.l(exit.o)
    0x08001712   0x08001712   0x00000002   PAD
    0x08001714   0x08001714   0x00000080   Code   RO         5923    .text               c_w.l(strcmpv7m.o)
    0x08001794   0x08001794   0x0000000c   Code   RO         5970    .text               c_w.l(sys_exit.o)
    0x080017a0   0x080017a0   0x00000002   Code   RO         5989    .text               c_w.l(use_no_semi.o)
    0x080017a2   0x080017a2   0x00000000   Code   RO         5991    .text               c_w.l(indicate_semi.o)
    0x080017a2   0x080017a2   0x0000003e   Code   RO         5873    CL$$btod_d2e        c_w.l(btod.o)
    0x080017e0   0x080017e0   0x00000046   Code   RO         5875    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001826   0x08001826   0x00000060   Code   RO         5874    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001886   0x08001886   0x00000338   Code   RO         5883    CL$$btod_div_common  c_w.l(btod.o)
    0x08001bbe   0x08001bbe   0x000000dc   Code   RO         5880    CL$$btod_e2e        c_w.l(btod.o)
    0x08001c9a   0x08001c9a   0x0000002a   Code   RO         5877    CL$$btod_ediv       c_w.l(btod.o)
    0x08001cc4   0x08001cc4   0x0000002a   Code   RO         5876    CL$$btod_emul       c_w.l(btod.o)
    0x08001cee   0x08001cee   0x00000244   Code   RO         5882    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001f32   0x08001f32   0x00000002   PAD
    0x08001f34   0x08001f34   0x00000068   Code   RO         5357    i.Angle_PID_control  pid_app.o
    0x08001f9c   0x08001f9c   0x00000002   Code   RO          511    i.BusFault_Handler  stm32f4xx_it.o
    0x08001f9e   0x08001f9e   0x00000002   PAD
    0x08001fa0   0x08001fa0   0x00000130   Code   RO         5526    i.Car_State_Update  scheduler_task.o
    0x080020d0   0x080020d0   0x0000000c   Code   RO          512    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x080020dc   0x080020dc   0x00000028   Code   RO         1549    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08002104   0x08002104   0x00000054   Code   RO         1550    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08002158   0x08002158   0x00000028   Code   RO         1551    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08002180   0x08002180   0x00000002   Code   RO          513    i.DebugMon_Handler  stm32f4xx_it.o
    0x08002182   0x08002182   0x00000002   PAD
    0x08002184   0x08002184   0x00000034   Code   RO         4900    i.Ebtn_Init         key_driver.o
    0x080021b8   0x080021b8   0x0000002c   Code   RO         4867    i.Encoder_Driver_Init  encoder_driver.o
    0x080021e4   0x080021e4   0x00000054   Code   RO         4868    i.Encoder_Driver_Update  encoder_driver.o
    0x08002238   0x08002238   0x00000028   Code   RO         5140    i.Encoder_Init      encoder_app.o
    0x08002260   0x08002260   0x00000018   Code   RO         5141    i.Encoder_Task      encoder_app.o
    0x08002278   0x08002278   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x0800227c   0x0800227c   0x00000002   Code   RO         5176    i.Gray_Init         gray_app.o
    0x0800227e   0x0800227e   0x00000002   PAD
    0x08002280   0x08002280   0x0000005c   Code   RO         5177    i.Gray_Task         gray_app.o
    0x080022dc   0x080022dc   0x00000092   Code   RO         1552    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x0800236e   0x0800236e   0x00000024   Code   RO         1553    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002392   0x08002392   0x00000002   PAD
    0x08002394   0x08002394   0x000001a0   Code   RO         1557    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002534   0x08002534   0x000000d4   Code   RO         1558    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08002608   0x08002608   0x0000006e   Code   RO         1562    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08002676   0x08002676   0x00000002   PAD
    0x08002678   0x08002678   0x00000024   Code   RO         1989    i.HAL_Delay         stm32f4xx_hal.o
    0x0800269c   0x0800269c   0x000001f0   Code   RO         1445    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x0800288c   0x0800288c   0x0000000a   Code   RO         1447    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x08002896   0x08002896   0x0000000a   Code   RO         1449    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x080028a0   0x080028a0   0x0000000c   Code   RO         1995    i.HAL_GetTick       stm32f4xx_hal.o
    0x080028ac   0x080028ac   0x00000188   Code   RO          646    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x08002a34   0x08002a34   0x00000204   Code   RO          664    i.HAL_I2C_Mem_Read  stm32f4xx_hal_i2c.o
    0x08002c38   0x08002c38   0x00000130   Code   RO          667    i.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x08002d68   0x08002d68   0x000000e8   Code   RO          338    i.HAL_I2C_MspInit   i2c.o
    0x08002e50   0x08002e50   0x00000010   Code   RO         2001    i.HAL_IncTick       stm32f4xx_hal.o
    0x08002e60   0x08002e60   0x00000034   Code   RO         2002    i.HAL_Init          stm32f4xx_hal.o
    0x08002e94   0x08002e94   0x00000040   Code   RO         2003    i.HAL_InitTick      stm32f4xx_hal.o
    0x08002ed4   0x08002ed4   0x00000030   Code   RO          611    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08002f04   0x08002f04   0x0000001a   Code   RO         1837    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08002f1e   0x08002f1e   0x00000002   PAD
    0x08002f20   0x08002f20   0x00000040   Code   RO         1843    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002f60   0x08002f60   0x00000024   Code   RO         1844    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002f84   0x08002f84   0x00000134   Code   RO         1091    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080030b8   0x080030b8   0x00000020   Code   RO         1098    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x080030d8   0x080030d8   0x00000020   Code   RO         1099    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x080030f8   0x080030f8   0x00000060   Code   RO         1100    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08003158   0x08003158   0x0000036c   Code   RO         1103    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080034c4   0x080034c4   0x00000028   Code   RO         1848    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080034ec   0x080034ec   0x00000002   Code   RO         2946    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x080034ee   0x080034ee   0x00000002   Code   RO         2947    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x080034f0   0x080034f0   0x00000054   Code   RO         2949    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08003544   0x08003544   0x00000090   Code   RO         2965    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x080035d4   0x080035d4   0x0000005a   Code   RO         2242    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x0800362e   0x0800362e   0x00000002   PAD
    0x08003630   0x08003630   0x00000054   Code   RO          386    i.HAL_TIM_Base_MspInit  tim.o
    0x08003684   0x08003684   0x00000080   Code   RO         2247    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08003704   0x08003704   0x000000dc   Code   RO         2251    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x080037e0   0x080037e0   0x000000a4   Code   RO         2263    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08003884   0x08003884   0x000000a4   Code   RO          388    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08003928   0x08003928   0x0000008e   Code   RO         2266    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x080039b6   0x080039b6   0x00000002   Code   RO         2276    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x080039b8   0x080039b8   0x00000130   Code   RO         2290    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08003ae8   0x08003ae8   0x00000054   Code   RO          389    i.HAL_TIM_MspPostInit  tim.o
    0x08003b3c   0x08003b3c   0x00000002   Code   RO         2293    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x08003b3e   0x08003b3e   0x000000cc   Code   RO         2314    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08003c0a   0x08003c0a   0x0000005a   Code   RO         2317    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08003c64   0x08003c64   0x00000002   Code   RO         2319    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08003c66   0x08003c66   0x00000002   Code   RO         2320    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x08003c68   0x08003c68   0x000000c8   Code   RO         2322    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08003d30   0x08003d30   0x000000f8   Code   RO         5527    i.HAL_TIM_PeriodElapsedCallback  scheduler_task.o
    0x08003e28   0x08003e28   0x00000002   Code   RO         2333    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x08003e2a   0x08003e2a   0x0000004a   Code   RO         3223    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x08003e74   0x08003e74   0x0000004c   Code   RO         5067    i.HAL_UARTEx_RxEventCallback  uart_driver.o
    0x08003ec0   0x08003ec0   0x00000070   Code   RO         3237    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x08003f30   0x08003f30   0x00000002   Code   RO         3239    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08003f32   0x08003f32   0x00000002   PAD
    0x08003f34   0x08003f34   0x00000280   Code   RO         3242    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080041b4   0x080041b4   0x00000064   Code   RO         3243    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08004218   0x08004218   0x00000100   Code   RO          464    i.HAL_UART_MspInit  usart.o
    0x08004318   0x08004318   0x00000002   Code   RO         3249    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x0800431a   0x0800431a   0x00000002   Code   RO         3250    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x0800431c   0x0800431c   0x000000a0   Code   RO         3251    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x080043bc   0x080043bc   0x00000002   Code   RO         3254    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x080043be   0x080043be   0x00000002   Code   RO          514    i.HardFault_Handler  stm32f4xx_it.o
    0x080043c0   0x080043c0   0x0000002e   Code   RO          689    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x080043ee   0x080043ee   0x00000002   PAD
    0x080043f0   0x080043f0   0x000000fc   Code   RO          699    i.I2C_RequestMemoryRead  stm32f4xx_hal_i2c.o
    0x080044ec   0x080044ec   0x000000a8   Code   RO          700    i.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x08004594   0x08004594   0x00000056   Code   RO          704    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080045ea   0x080045ea   0x00000002   PAD
    0x080045ec   0x080045ec   0x00000090   Code   RO          705    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x0800467c   0x0800467c   0x000000bc   Code   RO          706    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08004738   0x08004738   0x00000070   Code   RO          707    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080047a8   0x080047a8   0x00000056   Code   RO          708    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x080047fe   0x080047fe   0x00000002   PAD
    0x08004800   0x08004800   0x00000040   Code   RO         4164    i.IIC_Ack           iic.o
    0x08004840   0x08004840   0x0000000c   Code   RO         4166    i.IIC_Delay         iic.o
    0x0800484c   0x0800484c   0x0000003c   Code   RO         4167    i.IIC_GPIO_Init     iic.o
    0x08004888   0x08004888   0x00000014   Code   RO         4015    i.IIC_Get_Digtal    hardware_iic.o
    0x0800489c   0x0800489c   0x00000038   Code   RO         4168    i.IIC_NAck          iic.o
    0x080048d4   0x080048d4   0x00000024   Code   RO         4019    i.IIC_ReadBytes     hardware_iic.o
    0x080048f8   0x080048f8   0x00000058   Code   RO         4169    i.IIC_Read_Byte     iic.o
    0x08004950   0x08004950   0x0000005c   Code   RO         4170    i.IIC_Send_Byte     iic.o
    0x080049ac   0x080049ac   0x00000040   Code   RO         4171    i.IIC_Start         iic.o
    0x080049ec   0x080049ec   0x00000030   Code   RO         4172    i.IIC_Stop          iic.o
    0x08004a1c   0x08004a1c   0x00000048   Code   RO         4173    i.IIC_Wait_Ack      iic.o
    0x08004a64   0x08004a64   0x00000004   Code   RO         5212    i.Key_Init          key_app.o
    0x08004a68   0x08004a68   0x0000000e   Code   RO         5213    i.Key_Task          key_app.o
    0x08004a76   0x08004a76   0x00000002   PAD
    0x08004a78   0x08004a78   0x00000028   Code   RO         4942    i.Led_Display       led_driver.o
    0x08004aa0   0x08004aa0   0x00000006   Code   RO         5251    i.Led_Init          led_app.o
    0x08004aa6   0x08004aa6   0x00000002   PAD
    0x08004aa8   0x08004aa8   0x0000000c   Code   RO         5252    i.Led_Task          led_app.o
    0x08004ab4   0x08004ab4   0x00000068   Code   RO         5358    i.Line_PID_control  pid_app.o
    0x08004b1c   0x08004b1c   0x0000005c   Code   RO         5105    i.MPU_Get_Gyro_Offset  mpu6050_driver.o
    0x08004b78   0x08004b78   0x00000032   Code   RO         4779    i.MPU_Get_Gyroscope  mpu6050.o
    0x08004baa   0x08004baa   0x00000076   Code   RO         4781    i.MPU_Init          mpu6050.o
    0x08004c20   0x08004c20   0x0000003a   Code   RO         4782    i.MPU_Read_Byte     mpu6050.o
    0x08004c5a   0x08004c5a   0x00000002   PAD
    0x08004c5c   0x08004c5c   0x0000006c   Code   RO         4783    i.MPU_Read_Len      mpu6050.o
    0x08004cc8   0x08004cc8   0x0000000a   Code   RO         4784    i.MPU_Set_Accel_Fsr  mpu6050.o
    0x08004cd2   0x08004cd2   0x0000000a   Code   RO         4785    i.MPU_Set_Gyro_Fsr  mpu6050.o
    0x08004cdc   0x08004cdc   0x00000030   Code   RO         4786    i.MPU_Set_LPF       mpu6050.o
    0x08004d0c   0x08004d0c   0x0000002e   Code   RO         4787    i.MPU_Set_Rate      mpu6050.o
    0x08004d3a   0x08004d3a   0x0000003c   Code   RO         4788    i.MPU_Write_Byte    mpu6050.o
    0x08004d76   0x08004d76   0x00000052   Code   RO         4789    i.MPU_Write_Len     mpu6050.o
    0x08004dc8   0x08004dc8   0x0000002c   Code   RO          313    i.MX_DMA_Init       dma.o
    0x08004df4   0x08004df4   0x000000fc   Code   RO          289    i.MX_GPIO_Init      gpio.o
    0x08004ef0   0x08004ef0   0x00000040   Code   RO          339    i.MX_I2C1_Init      i2c.o
    0x08004f30   0x08004f30   0x00000040   Code   RO          340    i.MX_I2C3_Init      i2c.o
    0x08004f70   0x08004f70   0x000000d8   Code   RO          390    i.MX_TIM1_Init      tim.o
    0x08005048   0x08005048   0x00000064   Code   RO          391    i.MX_TIM2_Init      tim.o
    0x080050ac   0x080050ac   0x0000006c   Code   RO          392    i.MX_TIM3_Init      tim.o
    0x08005118   0x08005118   0x0000006c   Code   RO          393    i.MX_TIM4_Init      tim.o
    0x08005184   0x08005184   0x00000038   Code   RO          465    i.MX_USART1_UART_Init  usart.o
    0x080051bc   0x080051bc   0x00000038   Code   RO          466    i.MX_USART2_UART_Init  usart.o
    0x080051f4   0x080051f4   0x00000002   Code   RO          515    i.MemManage_Handler  stm32f4xx_it.o
    0x080051f6   0x080051f6   0x00000044   Code   RO         4970    i.Motor_Brake       motor_driver.o
    0x0800523a   0x0800523a   0x0000006e   Code   RO         4971    i.Motor_Config_Init  motor_driver.o
    0x080052a8   0x080052a8   0x0000001e   Code   RO         4972    i.Motor_Dead_Compensation  motor_driver.o
    0x080052c6   0x080052c6   0x00000002   PAD
    0x080052c8   0x080052c8   0x00000054   Code   RO         5287    i.Motor_Init        motor_app.o
    0x0800531c   0x0800531c   0x00000012   Code   RO         4973    i.Motor_Limit_Speed  motor_driver.o
    0x0800532e   0x0800532e   0x000000a4   Code   RO         4974    i.Motor_Set_Speed   motor_driver.o
    0x080053d2   0x080053d2   0x0000000e   Code   RO         5441    i.Mpu6050_Init      mpu6050_app.o
    0x080053e0   0x080053e0   0x00000024   Code   RO         5442    i.Mpu6050_Task      mpu6050_app.o
    0x08005404   0x08005404   0x00000002   Code   RO          516    i.NMI_Handler       stm32f4xx_it.o
    0x08005406   0x08005406   0x00000034   Code   RO         3865    i.OLED_Clear        oled.o
    0x0800543a   0x0800543a   0x00000002   PAD
    0x0800543c   0x0800543c   0x00000020   Code   RO         3871    i.OLED_Init         oled.o
    0x0800545c   0x0800545c   0x00000022   Code   RO         3874    i.OLED_Set_Pos      oled.o
    0x0800547e   0x0800547e   0x00000002   PAD
    0x08005480   0x08005480   0x0000009c   Code   RO         3876    i.OLED_ShowChar     oled.o
    0x0800551c   0x0800551c   0x00000058   Code   RO         3878    i.OLED_ShowString   oled.o
    0x08005574   0x08005574   0x00000024   Code   RO         3882    i.OLED_WR_CMD       oled.o
    0x08005598   0x08005598   0x00000024   Code   RO         3883    i.OLED_WR_DATA      oled.o
    0x080055bc   0x080055bc   0x0000000e   Code   RO         5323    i.Oled_Init         oled_app.o
    0x080055ca   0x080055ca   0x00000032   Code   RO         5028    i.Oled_Printf       oled_driver.o
    0x080055fc   0x080055fc   0x00000138   Code   RO         5324    i.Oled_Task         oled_app.o
    0x08005734   0x08005734   0x000000b4   Code   RO         5359    i.PID_Init          pid_app.o
    0x080057e8   0x080057e8   0x000000c8   Code   RO         5360    i.PID_Task          pid_app.o
    0x080058b0   0x080058b0   0x00000002   Code   RO          517    i.PendSV_Handler    stm32f4xx_it.o
    0x080058b2   0x080058b2   0x00000002   Code   RO          518    i.SVC_Handler       stm32f4xx_it.o
    0x080058b4   0x080058b4   0x00000014   Code   RO         5477    i.Scheduler_Init    scheduler.o
    0x080058c8   0x080058c8   0x0000003c   Code   RO         5478    i.Scheduler_Run     scheduler.o
    0x08005904   0x08005904   0x00000004   Code   RO          519    i.SysTick_Handler   stm32f4xx_it.o
    0x08005908   0x08005908   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x0800599c   0x0800599c   0x00000010   Code   RO         3577    i.SystemInit        system_stm32f4xx.o
    0x080059ac   0x080059ac   0x00000058   Code   RO         5528    i.System_Init       scheduler_task.o
    0x08005a04   0x08005a04   0x0000000c   Code   RO          520    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x08005a10   0x08005a10   0x000000d0   Code   RO         2335    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08005ae0   0x08005ae0   0x0000001a   Code   RO         2336    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x08005afa   0x08005afa   0x00000014   Code   RO         2346    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08005b0e   0x08005b0e   0x00000010   Code   RO         2347    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08005b1e   0x08005b1e   0x00000002   PAD
    0x08005b20   0x08005b20   0x00000060   Code   RO         2348    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08005b80   0x08005b80   0x0000006c   Code   RO         2349    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08005bec   0x08005bec   0x00000068   Code   RO         2350    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08005c54   0x08005c54   0x00000050   Code   RO         2351    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08005ca4   0x08005ca4   0x00000022   Code   RO         2353    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005cc6   0x08005cc6   0x00000024   Code   RO         2355    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08005cea   0x08005cea   0x0000000e   Code   RO         3256    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08005cf8   0x08005cf8   0x0000004a   Code   RO         3257    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08005d42   0x08005d42   0x00000086   Code   RO         3258    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08005dc8   0x08005dc8   0x0000001e   Code   RO         3260    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08005de6   0x08005de6   0x0000004e   Code   RO         3266    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08005e34   0x08005e34   0x0000001c   Code   RO         3267    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08005e50   0x08005e50   0x000000c2   Code   RO         3268    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08005f12   0x08005f12   0x00000002   PAD
    0x08005f14   0x08005f14   0x0000010c   Code   RO         3269    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08006020   0x08006020   0x000000a0   Code   RO         3270    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x080060c0   0x080060c0   0x00000072   Code   RO         3272    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08006132   0x08006132   0x00000002   PAD
    0x08006134   0x08006134   0x0000000c   Code   RO          521    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08006140   0x08006140   0x0000000c   Code   RO          522    i.USART2_IRQHandler  stm32f4xx_it.o
    0x0800614c   0x0800614c   0x00000038   Code   RO         5407    i.Uart_Init         uart_app.o
    0x08006184   0x08006184   0x00000032   Code   RO         5068    i.Uart_Printf       uart_driver.o
    0x080061b6   0x080061b6   0x00000002   Code   RO          523    i.UsageFault_Handler  stm32f4xx_it.o
    0x080061b8   0x080061b8   0x00000030   Code   RO         5793    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080061e8   0x080061e8   0x00000020   Code   RO         1850    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08006208   0x08006208   0x00000360   Code   RO         5612    i.__hardfp_asin     m_wm.l(asin.o)
    0x08006568   0x08006568   0x000002d8   Code   RO         5759    i.__hardfp_atan     m_wm.l(atan.o)
    0x08006840   0x08006840   0x00000200   Code   RO         5626    i.__hardfp_atan2    m_wm.l(atan2.o)
    0x08006a40   0x08006a40   0x000000f8   Code   RO         5795    i.__kernel_poly     m_wm.l(poly.o)
    0x08006b38   0x08006b38   0x00000014   Code   RO         5774    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x08006b4c   0x08006b4c   0x00000014   Code   RO         5775    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x08006b60   0x08006b60   0x00000020   Code   RO         5776    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08006b80   0x08006b80   0x00000020   Code   RO         5779    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08006ba0   0x08006ba0   0x0000000e   Code   RO         5705    i._is_digit         c_w.l(__printf_wp.o)
    0x08006bae   0x08006bae   0x00000002   PAD
    0x08006bb0   0x08006bb0   0x00000090   Code   RO         4241    i.accel_self_test   inv_mpu.o
    0x08006c40   0x08006c40   0x00000010   Code   RO         5761    i.atan              m_wm.l(atan.o)
    0x08006c50   0x08006c50   0x00000026   Code   RO         3611    i.bit_array_and     ebtn.o
    0x08006c76   0x08006c76   0x0000001e   Code   RO         3612    i.bit_array_assign  ebtn.o
    0x08006c94   0x08006c94   0x0000000c   Code   RO         3613    i.bit_array_cmp     ebtn.o
    0x08006ca0   0x08006ca0   0x00000012   Code   RO         3614    i.bit_array_get     ebtn.o
    0x08006cb2   0x08006cb2   0x00000002   PAD
    0x08006cb4   0x08006cb4   0x00000026   Code   RO         3615    i.bit_array_or      ebtn.o
    0x08006cda   0x08006cda   0x00000002   PAD
    0x08006cdc   0x08006cdc   0x00000060   Code   RO         5106    i.convert_to_continuous_yaw  mpu6050_driver.o
    0x08006d3c   0x08006d3c   0x0000003a   Code   RO         4593    i.dmp_enable_6x_lp_quat  inv_mpu_dmp_motion_driver.o
    0x08006d76   0x08006d76   0x00000002   PAD
    0x08006d78   0x08006d78   0x000001d8   Code   RO         4594    i.dmp_enable_feature  inv_mpu_dmp_motion_driver.o
    0x08006f50   0x08006f50   0x0000003c   Code   RO         4595    i.dmp_enable_gyro_cal  inv_mpu_dmp_motion_driver.o
    0x08006f8c   0x08006f8c   0x0000003a   Code   RO         4596    i.dmp_enable_lp_quat  inv_mpu_dmp_motion_driver.o
    0x08006fc6   0x08006fc6   0x00000002   PAD
    0x08006fc8   0x08006fc8   0x00000014   Code   RO         4601    i.dmp_load_motion_driver_firmware  inv_mpu_dmp_motion_driver.o
    0x08006fdc   0x08006fdc   0x00000168   Code   RO         4602    i.dmp_read_fifo     inv_mpu_dmp_motion_driver.o
    0x08007144   0x08007144   0x000000d8   Code   RO         4605    i.dmp_set_accel_bias  inv_mpu_dmp_motion_driver.o
    0x0800721c   0x0800721c   0x00000064   Code   RO         4606    i.dmp_set_fifo_rate  inv_mpu_dmp_motion_driver.o
    0x08007280   0x08007280   0x000000cc   Code   RO         4607    i.dmp_set_gyro_bias  inv_mpu_dmp_motion_driver.o
    0x0800734c   0x0800734c   0x0000010c   Code   RO         4609    i.dmp_set_orientation  inv_mpu_dmp_motion_driver.o
    0x08007458   0x08007458   0x00000030   Code   RO         4612    i.dmp_set_shake_reject_thresh  inv_mpu_dmp_motion_driver.o
    0x08007488   0x08007488   0x00000020   Code   RO         4613    i.dmp_set_shake_reject_time  inv_mpu_dmp_motion_driver.o
    0x080074a8   0x080074a8   0x00000020   Code   RO         4614    i.dmp_set_shake_reject_timeout  inv_mpu_dmp_motion_driver.o
    0x080074c8   0x080074c8   0x00000040   Code   RO         4615    i.dmp_set_tap_axes  inv_mpu_dmp_motion_driver.o
    0x08007508   0x08007508   0x00000022   Code   RO         4616    i.dmp_set_tap_count  inv_mpu_dmp_motion_driver.o
    0x0800752a   0x0800752a   0x00000002   PAD
    0x0800752c   0x0800752c   0x00000160   Code   RO         4617    i.dmp_set_tap_thresh  inv_mpu_dmp_motion_driver.o
    0x0800768c   0x0800768c   0x00000020   Code   RO         4618    i.dmp_set_tap_time  inv_mpu_dmp_motion_driver.o
    0x080076ac   0x080076ac   0x00000020   Code   RO         4619    i.dmp_set_tap_time_multi  inv_mpu_dmp_motion_driver.o
    0x080076cc   0x080076cc   0x00000044   Code   RO         3627    i.ebtn_init         ebtn.o
    0x08007710   0x08007710   0x00000064   Code   RO         3631    i.ebtn_process      ebtn.o
    0x08007774   0x08007774   0x0000002e   Code   RO         3632    i.ebtn_process_btn  ebtn.o
    0x080077a2   0x080077a2   0x00000002   PAD
    0x080077a4   0x080077a4   0x000000ac   Code   RO         3633    i.ebtn_process_btn_combo  ebtn.o
    0x08007850   0x08007850   0x00000158   Code   RO         3634    i.ebtn_process_with_curr_state  ebtn.o
    0x080079a8   0x080079a8   0x0000000c   Code   RO         3636    i.ebtn_set_config   ebtn.o
    0x080079b4   0x080079b4   0x00000018   Code   RO         5789    i.fabs              m_wm.l(fabs.o)
    0x080079cc   0x080079cc   0x000000b4   Code   RO         4242    i.get_accel_prod_shift  inv_mpu.o
    0x08007a80   0x08007a80   0x00000314   Code   RO         4243    i.get_st_biases     inv_mpu.o
    0x08007d94   0x08007d94   0x000000d4   Code   RO         4244    i.gyro_self_test    inv_mpu.o
    0x08007e68   0x08007e68   0x00000022   Code   RO         4245    i.inv_orientation_matrix_to_scalar  inv_mpu.o
    0x08007e8a   0x08007e8a   0x00000048   Code   RO         4246    i.inv_row_2_scale   inv_mpu.o
    0x08007ed2   0x08007ed2   0x0000003a   Code   RO           15    i.main              main.o
    0x08007f0c   0x08007f0c   0x00000002   Code   RO         4247    i.mget_ms           inv_mpu.o
    0x08007f0e   0x08007f0e   0x00000002   PAD
    0x08007f10   0x08007f10   0x00000054   Code   RO         4248    i.mpu_configure_fifo  inv_mpu.o
    0x08007f64   0x08007f64   0x00000164   Code   RO         4249    i.mpu_dmp_get_data  inv_mpu.o
    0x080080c8   0x080080c8   0x000000ac   Code   RO         4250    i.mpu_dmp_init      inv_mpu.o
    0x08008174   0x08008174   0x0000003c   Code   RO         4251    i.mpu_get_accel_fsr  inv_mpu.o
    0x080081b0   0x080081b0   0x00000044   Code   RO         4253    i.mpu_get_accel_sens  inv_mpu.o
    0x080081f4   0x080081f4   0x00000034   Code   RO         4259    i.mpu_get_gyro_fsr  inv_mpu.o
    0x08008228   0x08008228   0x0000004c   Code   RO         4261    i.mpu_get_gyro_sens  inv_mpu.o
    0x08008274   0x08008274   0x00000038   Code   RO         4263    i.mpu_get_lpf       inv_mpu.o
    0x080082ac   0x080082ac   0x0000001c   Code   RO         4265    i.mpu_get_sample_rate  inv_mpu.o
    0x080082c8   0x080082c8   0x00000124   Code   RO         4267    i.mpu_init          inv_mpu.o
    0x080083ec   0x080083ec   0x000000a0   Code   RO         4268    i.mpu_load_firmware  inv_mpu.o
    0x0800848c   0x0800848c   0x000000b8   Code   RO         4269    i.mpu_lp_accel_mode  inv_mpu.o
    0x08008544   0x08008544   0x00000090   Code   RO         4272    i.mpu_read_fifo_stream  inv_mpu.o
    0x080085d4   0x080085d4   0x00000054   Code   RO         4273    i.mpu_read_mem      inv_mpu.o
    0x08008628   0x08008628   0x00000160   Code   RO         4276    i.mpu_reset_fifo    inv_mpu.o
    0x08008788   0x08008788   0x000000f0   Code   RO         4277    i.mpu_run_self_test  inv_mpu.o
    0x08008878   0x08008878   0x00000068   Code   RO         4279    i.mpu_set_accel_fsr  inv_mpu.o
    0x080088e0   0x080088e0   0x000000dc   Code   RO         4280    i.mpu_set_bypass    inv_mpu.o
    0x080089bc   0x080089bc   0x00000078   Code   RO         4282    i.mpu_set_dmp_state  inv_mpu.o
    0x08008a34   0x08008a34   0x0000006c   Code   RO         4283    i.mpu_set_gyro_fsr  inv_mpu.o
    0x08008aa0   0x08008aa0   0x00000064   Code   RO         4284    i.mpu_set_int_latched  inv_mpu.o
    0x08008b04   0x08008b04   0x0000006c   Code   RO         4286    i.mpu_set_lpf       inv_mpu.o
    0x08008b70   0x08008b70   0x00000080   Code   RO         4287    i.mpu_set_sample_rate  inv_mpu.o
    0x08008bf0   0x08008bf0   0x000000b8   Code   RO         4288    i.mpu_set_sensors   inv_mpu.o
    0x08008ca8   0x08008ca8   0x00000054   Code   RO         4289    i.mpu_write_mem     inv_mpu.o
    0x08008cfc   0x08008cfc   0x0000001c   Code   RO         4902    i.my_get_key_state  key_driver.o
    0x08008d18   0x08008d18   0x00000050   Code   RO         5214    i.my_handle_key_event  key_app.o
    0x08008d68   0x08008d68   0x00000066   Code   RO         4095    i.pid_calculate_positional  pid.o
    0x08008dce   0x08008dce   0x00000020   Code   RO         4096    i.pid_constrain     pid.o
    0x08008dee   0x08008dee   0x00000002   PAD
    0x08008df0   0x08008df0   0x00000030   Code   RO         4097    i.pid_init          pid.o
    0x08008e20   0x08008e20   0x00000026   Code   RO         4098    i.pid_out_limit     pid.o
    0x08008e46   0x08008e46   0x00000002   PAD
    0x08008e48   0x08008e48   0x00000028   Code   RO         4099    i.pid_reset         pid.o
    0x08008e70   0x08008e70   0x00000006   Code   RO         4102    i.pid_set_target    pid.o
    0x08008e76   0x08008e76   0x00000002   PAD
    0x08008e78   0x08008e78   0x0000014c   Code   RO         3637    i.prv_process_btn   ebtn.o
    0x08008fc4   0x08008fc4   0x00000030   Code   RO         3779    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08008ff4   0x08008ff4   0x00000026   Code   RO         3782    i.rt_ringbuffer_init  ringbuffer.o
    0x0800901a   0x0800901a   0x00000072   Code   RO         3784    i.rt_ringbuffer_put  ringbuffer.o
    0x0800908c   0x0800908c   0x00000020   Code   RO         3789    i.rt_ringbuffer_status  ringbuffer.o
    0x080090ac   0x080090ac   0x00000088   Code   RO         4290    i.run_self_test     inv_mpu.o
    0x08009134   0x08009134   0x00000058   Code   RO         4291    i.set_int_enable    inv_mpu.o
    0x0800918c   0x0800918c   0x0000006e   Code   RO         5800    i.sqrt              m_wm.l(sqrt.o)
    0x080091fa   0x080091fa   0x00000002   PAD
    0x080091fc   0x080091fc   0x0000002c   Code   RO         5896    locale$$code        c_w.l(lc_numeric_c.o)
    0x08009228   0x08009228   0x0000002c   Code   RO         5917    locale$$code        c_w.l(lc_ctype_c.o)
    0x08009254   0x08009254   0x00000018   Code   RO         5738    x$fpl$basic         fz_wm.l(basic.o)
    0x0800926c   0x0800926c   0x00000062   Code   RO         5590    x$fpl$d2f           fz_wm.l(d2f.o)
    0x080092ce   0x080092ce   0x00000002   PAD
    0x080092d0   0x080092d0   0x00000150   Code   RO         5592    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08009420   0x08009420   0x00000018   Code   RO         5850    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08009438   0x08009438   0x000002b0   Code   RO         5599    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x080096e8   0x080096e8   0x00000078   Code   RO         5740    x$fpl$deqf          fz_wm.l(deqf.o)
    0x08009760   0x08009760   0x00000026   Code   RO         5602    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08009786   0x08009786   0x00000002   PAD
    0x08009788   0x08009788   0x00000154   Code   RO         5608    x$fpl$dmul          fz_wm.l(dmul.o)
    0x080098dc   0x080098dc   0x0000009c   Code   RO         5742    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08009978   0x08009978   0x0000000c   Code   RO         5744    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08009984   0x08009984   0x00000016   Code   RO         5593    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x0800999a   0x0800999a   0x00000002   PAD
    0x0800999c   0x0800999c   0x00000198   Code   RO         5854    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x08009b34   0x08009b34   0x000001d4   Code   RO         5594    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08009d08   0x08009d08   0x00000056   Code   RO         5610    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08009d5e   0x08009d5e   0x0000008c   Code   RO         5746    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08009dea   0x08009dea   0x0000000a   Code   RO         5985    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x08009df4   0x08009df4   0x0000000a   Code   RO         5748    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x08009dfe   0x08009dfe   0x00000004   Code   RO         5750    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08009e02   0x08009e02   0x00000004   Code   RO         5752    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08009e06   0x08009e06   0x00000000   Code   RO         5758    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08009e06   0x08009e06   0x00000008   Data   RO         1564    .constdata          stm32f4xx_hal_dma.o
    0x08009e0e   0x08009e0e   0x00000010   Data   RO         3578    .constdata          system_stm32f4xx.o
    0x08009e1e   0x08009e1e   0x00000008   Data   RO         3579    .constdata          system_stm32f4xx.o
    0x08009e26   0x08009e26   0x0000001b   Data   RO         4293    .constdata          inv_mpu.o
    0x08009e41   0x08009e41   0x00000001   PAD
    0x08009e42   0x08009e42   0x0000000c   Data   RO         4294    .constdata          inv_mpu.o
    0x08009e4e   0x08009e4e   0x00000002   PAD
    0x08009e50   0x08009e50   0x00000028   Data   RO         4295    .constdata          inv_mpu.o
    0x08009e78   0x08009e78   0x00000bf6   Data   RO         4621    .constdata          inv_mpu_dmp_motion_driver.o
    0x0800aa6e   0x0800aa6e   0x0000000e   Data   RO         4903    .constdata          key_driver.o
    0x0800aa7c   0x0800aa7c   0x00000004   PAD
    0x0800aa80   0x0800aa80   0x00000050   Data   RO         5615    .constdata          m_wm.l(asin.o)
    0x0800aad0   0x0800aad0   0x00000008   Data   RO         5664    .constdata          c_w.l(_printf_wctomb.o)
    0x0800aad8   0x0800aad8   0x00000028   Data   RO         5693    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800ab00   0x0800ab00   0x00000011   Data   RO         5713    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800ab11   0x0800ab11   0x00000007   PAD
    0x0800ab18   0x0800ab18   0x00000098   Data   RO         5762    .constdata          m_wm.l(atan.o)
    0x0800abb0   0x0800abb0   0x00000008   Data   RO         5797    .constdata          m_wm.l(qnan.o)
    0x0800abb8   0x0800abb8   0x00000026   Data   RO         5839    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800abde   0x0800abde   0x00000002   PAD
    0x0800abe0   0x0800abe0   0x00000094   Data   RO         5871    .constdata          c_w.l(bigflt0.o)
    0x0800ac74   0x0800ac74   0x00000020   Data   RO         6043    Region$$Table       anon$$obj.o
    0x0800ac94   0x0800ac94   0x0000001c   Data   RO         5895    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800acb0   0x0800acb0   0x00000110   Data   RO         5916    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800adc0, Size: 0x00001518, Max: 0x0001c000, ABSOLUTE, COMPRESSED[0x000005a8])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x0000000c   Data   RW         2009    .data               stm32f4xx_hal.o
    0x2000000c   COMPRESSED   0x00000004   Data   RW         3580    .data               system_stm32f4xx.o
    0x20000010   COMPRESSED   0x0000081f   Data   RW         3886    .data               oled.o
    0x2000082f   COMPRESSED   0x00000001   PAD
    0x20000830   COMPRESSED   0x00000035   Data   RW         4296    .data               inv_mpu.o
    0x20000865   COMPRESSED   0x00000003   PAD
    0x20000868   COMPRESSED   0x0000001c   Data   RW         4904    .data               key_driver.o
    0x20000884   COMPRESSED   0x00000001   Data   RW         4943    .data               led_driver.o
    0x20000885   COMPRESSED   0x00000003   PAD
    0x20000888   COMPRESSED   0x0000000c   Data   RW         5107    .data               mpu6050_driver.o
    0x20000894   COMPRESSED   0x00000028   Data   RW         5178    .data               gray_app.o
    0x200008bc   COMPRESSED   0x00000001   Data   RW         5253    .data               led_app.o
    0x200008bd   COMPRESSED   0x00000003   PAD
    0x200008c0   COMPRESSED   0x00000058   Data   RW         5362    .data               pid_app.o
    0x20000918   COMPRESSED   0x0000000c   Data   RW         5443    .data               mpu6050_app.o
    0x20000924   COMPRESSED   0x0000001c   Data   RW         5479    .data               scheduler.o
    0x20000940   COMPRESSED   0x00000018   Data   RW         5529    .data               scheduler_task.o
    0x20000958        -       0x000000a8   Zero   RW          341    .bss                i2c.o
    0x20000a00        -       0x00000120   Zero   RW          394    .bss                tim.o
    0x20000b20        -       0x000000f0   Zero   RW          467    .bss                usart.o
    0x20000c10        -       0x00000034   Zero   RW         3638    .bss                ebtn.o
    0x20000c44        -       0x00000010   Zero   RW         4620    .bss                inv_mpu_dmp_motion_driver.o
    0x20000c54        -       0x0000008c   Zero   RW         5069    .bss                uart_driver.o
    0x20000ce0        -       0x00000080   Zero   RW         5070    .bss                uart_driver.o
    0x20000d60        -       0x00000020   Zero   RW         5142    .bss                encoder_app.o
    0x20000d80        -       0x00000048   Zero   RW         5289    .bss                motor_app.o
    0x20000dc8        -       0x000000f0   Zero   RW         5361    .bss                pid_app.o
    0x20000eb8        -       0x00000060   Zero   RW         5857    .bss                c_w.l(libspace.o)
    0x20000f18        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20001118        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0800b368, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        44          4          0          0          0        742   dma.o
      1210         22          0          0         52      17643   ebtn.o
        64         16          0          0         32       1215   encoder_app.o
       128         18          0          0          0       2046   encoder_driver.o
       252         16          0          0          0       1031   gpio.o
        94         10          0         40          0       1704   gray_app.o
        56          4          0          0          0       1772   hardware_iic.o
       360         52          0          0        168       2326   i2c.o
       556         46          0          0          0       5743   iic.o
      5220        226         79         53          0      31306   inv_mpu.o
      2442        134       3062          0         16      20154   inv_mpu_dmp_motion_driver.o
        98         26          0          0          0       1740   key_app.o
        80         16         14         28          0       1719   key_driver.o
        18          4          0          1          0       1038   led_app.o
        40          8          0          1          0        574   led_driver.o
       210         10          0          0          0     710705   main.o
        84         14          0          0         72        746   motor_app.o
       390          0          0          0          0       4238   motor_driver.o
       590          0          0          0          0       6913   mpu6050.o
        50          6          0         12          0       1207   mpu6050_app.o
       188         16          0         12          0       1814   mpu6050_driver.o
       434         20          0       2079          0       5531   oled.o
       326        110          0          0          0        988   oled_app.o
        50          0          0          0          0       1382   oled_driver.o
       266         12          0          0          0       4455   pid.o
       588         64          0         88        240       3324   pid_app.o
       232          0          0          0          0       5378   ringbuffer.o
        80         10          0         28          0       1632   scheduler.o
       640        116          0         24          0       3061   scheduler_task.o
        64         26        392          0       1536        828   startup_stm32f407xx.o
       180         28          0         12          0       9273   stm32f4xx_hal.o
       198         14          0          0          0      33711   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7194   stm32f4xx_hal_dma.o
       516         46          0          0          0       2787   stm32f4xx_hal_gpio.o
      2294         52          0          0          0      12998   stm32f4xx_hal_i2c.o
        48          6          0          0          0        826   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5156   stm32f4xx_hal_rcc.o
      2280        136          0          0          0      18605   stm32f4xx_hal_tim.o
       232         28          0          0          0       3221   stm32f4xx_hal_tim_ex.o
      2188         28          0          0          0      15388   stm32f4xx_hal_uart.o
        68         24          0          0          0       5822   stm32f4xx_it.o
        16          4         24          4          0       1079   system_stm32f4xx.o
       864         70          0          0        288       5129   tim.o
        56         20          0          0          0        460   uart_app.o
       126         16          0          0        268       2027   uart_driver.o
       368         46          0          0        240       2410   usart.o

    ----------------------------------------------------------------------
     26776       <USER>       <GROUP>       2392       2912     969041   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        60          0          7         10          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
        16          0          0          0          0         68   aeabi_memset.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        72          0          0          0          0         76   llsdiv.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        88          0          0          0          0         76   memcmp.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       826         16          0          0          0        492   daddsub_clz.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
       120          4          0          0          0        140   deqf.o
        38          0          0          0          0        116   dflt_clz.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
       864         94         80          0          0        280   asin.o
       744        106        152          0          0        352   atan.o
       512         64          0          0          0        208   atan2.o
       104         16          0          0          0        496   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o
       110          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
     13286        <USER>        <GROUP>          0         96       9360   Library Totals
        26          0          9          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7618        270        551          0         96       4748   c_w.l
      2988        244          0          0          0       2728   fz_wm.l
      2654        280        240          0          0       1884   m_wm.l

    ----------------------------------------------------------------------
     13286        <USER>        <GROUP>          0         96       9360   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     40062       2406       4418       2392       3008     948349   Grand Totals
     40062       2406       4418       1448       3008     948349   ELF Image Totals (compressed)
     40062       2406       4418       1448          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                44480 (  43.44kB)
    Total RW  Size (RW Data + ZI Data)              5400 (   5.27kB)
    Total ROM Size (Code + RO Data + RW Data)      45928 (  44.85kB)

==============================================================================

